
services:

  redis:
    image: redis:7
    restart: always
    environment:
      REDISCLI_AUTH: ${REDIS_PASSWORD:-123456}
    volumes:
      # Mount the redis data directory to the container.
      - ./volumes/redis/data:/data
    # Set the redis password when startup redis server.
    command: redis-server --requirepass ${REDIS_PASSWORD:-123456}
    ports:
      - "6379:6379"
    healthcheck:
      test: [ 'CMD', 'redis-cli', 'ping' ]
