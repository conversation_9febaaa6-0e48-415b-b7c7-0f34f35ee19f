---
description:
globs:
alwaysApply: true
---
---
description: FastAPI MCP Server Project Rules
ruleType: Always
globs: ["**/*.py"]
---
## Key Principles
- Write concise, technical responses with accurate Python examples.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., is_active, has_permission).
- Use lowercase with underscores for directories and files (e.g., routers/user_routes.py).
- Favor named exports for routes and utility functions.
- Prefer Pydantic models over raw dictionaries for input validation.
- Use Pydantic v2

## Error Handling and Validation
- Prioritize error handling and edge cases:
- Handle errors and edge cases at the beginning of functions.
- Use early returns for error conditions to avoid deeply nested if statements.
- Place the happy path last in the function for improved readability.
- Avoid unnecessary else statements; use the if-return pattern instead.
- Use guard clauses to handle preconditions and invalid states early.
- Implement proper error logging and user-friendly error messages.
- Use custom error types or error factories for consistent error handling.

## Project Stack & Conventions

- **Python**: Minimum Python 3.12
- **Framework**: FastAPI for backend API development
- **Dependency Management**: Use uv for all dependency installation, updates, and environment management
- **Git Pre-commit Hook**: Use pre-commit hooks for code quality (linting, formatting, etc.)
- **Static Analysis**: Use Ruff for linting and static code analysis
- **Environment Management**: Use Pydantic for environment variable validation and management
- **Containerization**: Use Docker for deployment and local development
- **API Documentation**: Use Swagger (FastAPI auto-generated) only in debug mode
- **Performance Tracing**: Use OpenTelemetry for distributed tracing and observability
- **Security**: Do not use any packages with known vulnerabilities; regularly audit dependencies
- **Development Environment**: Use Cursor with these rules for consistent code generation and review

## Code Quality & Style

- **Clean Code**: Prioritize readability, modularity, and maintainability
- **Type Hints**: Use Python type hints for all functions and variables
- **Pydantic Models**: Prefer Pydantic models over raw dictionaries for input/output validation
- **Functional Programming**: Use functional, declarative patterns; avoid unnecessary classes
- **Error Handling**: Handle errors early, use descriptive error messages, and log appropriately
- **Testing**: Write unit and integration tests for all critical paths; use pytest
- **Performance**: Minimize blocking I/O; use async/await for database and external API calls
- **Logging**: Use structured logging for observability and debugging

## FastAPI & MCP Specifics

- **Routes**: Organize routes by feature using APIRouter
- **Dependencies**: Use FastAPI’s dependency injection for shared logic
- **MCP Tools**: Expose tools via FastAPI endpoints; use fastapi_mcp or equivalent for MCP integration
- **Schema Validation**: Use Pydantic for request/response validation
- **Middleware**: Use middleware for logging, tracing, and error handling
- **Swagger**: Enable Swagger docs only in debug mode
- Use declarative route definitions with clear return type annotations.
- Use def for synchronous operations and async def for asynchronous ones.
- Minimize @app.on_event("startup") and @app.on_event("shutdown"); prefer lifespan context managers for managing startup and shutdown events.
- Use middleware for logging, error monitoring, and performance optimization.
- Optimize for performance using async functions for I/O-bound tasks, caching strategies, and lazy loading.
- Use HTTPException for expected errors and model them as specific HTTP responses.
- Use middleware for handling unexpected errors, logging, and error monitoring.
- Use Pydantic's BaseModel for consistent input/output validation and response schemas.

## Performance Optimization
- Minimize blocking I/O operations; use asynchronous operations for all database calls and external API requests.
- Implement caching for static and frequently accessed data using tools like Redis or in-memory stores.
- Optimize data serialization and deserialization with Pydantic.
- Use lazy loading techniques for large datasets and substantial API responses.

## Key Conventions
- Rely on FastAPI’s dependency injection system for managing state and shared resources.
- Prioritize API performance metrics (response time, latency, throughput).
- Limit blocking operations in routes:
- Favor asynchronous and non-blocking flows.
- Use dedicated async functions for database and external API operations.
- Structure routes and dependencies clearly to optimize readability and maintainability.

## MCP
- for mcp implementation, we'll be using fastmcp(https://gofastmcp.com/getting-started/welcome)
- We will be building mcp on top of our FastAPI web application

Sample integration as below for Mounting FastMCP in FastAPI Applications
You can integrate your FastMCP server into existing FastAPI applications by mounting it as a sub-application: We'll be using this approach for prompts
```
from fastmcp import FastMCP
from fastapi import FastAPI
from starlette.routing import Mount

# Create your FastMCP server with tools, resources, etc.
mcp = FastMCP("MyServer")

@mcp.tool
def hello(name: str) -> str:
    return f"Hello, {name}!"

# Create the ASGI app
mcp_app = mcp.http_app(path='/mcp')

# Create a FastAPI app and mount the MCP server
app = FastAPI(lifespan=mcp_app.lifespan)
app.mount("/mcp-server", mcp_app)
```

## Implement search logic
return {"results": []}

app.include_router(router)


## Security & Best Practices

- **Dependency Auditing**: Regularly audit dependencies with tools like pip-audit or uv audit
- **Environment Secrets**: Never hardcode secrets; use Pydantic for env var validation
- **Traceability**: Use OpenTelemetry for performance tracing and observability
- **Code Reviews**: Review code with Cursor and pre-commit hooks before merging
- **security issues in Python code** : Bandit is used to review. So make sure to run bandit for new or modified files when pushing to git

## Tooling Commands

- **uv**: Use `uv add <package>`, `uv sync`, `uv run ...`
- **Ruff**: Use `ruff check .` and `ruff format .`
- **pre-commit**: Configure pre-commit hooks for linting, formatting, and security checks
- **Docker**: Use `docker build` and `docker compose` for containerization

## Push to git

When pushing changes to Git, please use clear and meaningful commit messages that accurately describe the changes made.

## Project Structure Overview

```
src/
├── mcp/               # Core logic for the MCP server
│   ├── tools/         # Tool definitions and handlers used by MCP
│   ├── prompts/       # Prompt templates and instructions for agents
├── models/            # Response data models (e.g., Pydantic schemas)
├── entities/          # MongoDB document schemas and database models
├── routes/            # API route definitions
├── services/          # Business logic and service components
├── utils/             # Shared utility functions and helpers
```

### Notes:

* `src/mcp` contains everything specific to the MCP (Model Context Protocol) logic and agent functionality.
* `tools` and `prompts` under `mcp` modularize agent behaviors and prompt engineering.
* `models` and `entities` separate external response formats from internal DB schemas.
* `routes`, `services`, and `utils` follow a typical FastAPI service-oriented structure.
