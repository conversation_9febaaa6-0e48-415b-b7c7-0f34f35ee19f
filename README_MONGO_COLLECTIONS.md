# MongoDB Collections Reference

This document lists all MongoDB collections used in this project, their corresponding Java model classes, and a detailed schema for each collection, including field names, types, and brief descriptions.

---

## Collections Overview

| Collection Name      | Java Model Class                | Description |
|---------------------|---------------------------------|-------------|
| `buildings`         | Building                       | Represents buildings, with fields for name, status, default floor, and site linkage. |
| `categories`        | Category, Config               | Stores location/event categories and configuration data. |
| `floors`            | Floor                          | Represents floors within buildings, including geometry, images, and metadata. |
| `locations`         | Location                       | Represents locations (tenants, amenities, etc.), with rich metadata and links. |
| `nodes`             | Node                           | Represents graph nodes for navigation, with coordinates and metadata. |
| `sites`             | Site                           | Represents sites (malls, campuses, etc.), with address, languages, and settings. |


### buildings
- **Model:** `Building`
- **Purpose:** Represents buildings within a site.

| Field           | Type      | Description                       |
|-----------------|-----------|-----------------------------------|
| buildingId      | String    | Unique building ID                |
| name            | String    | Building name                     |
| siteId          | String    | Associated site ID                |

### categories
- **Model:** `Category`, `Config`
- **Purpose:** Stores categories for locations/events and configuration data.

| Field (Category)      | Type         | Description                       |
|----------------------|--------------|-----------------------------------|
| categoryId           | String       | Unique category ID                |
| name                 | String       | Category name                     |
| siteId               | String       | Associated site ID                |

### floors
- **Model:** `Floor`
- **Purpose:** Represents floors within buildings, including geometry and metadata.

| Field           | Type                | Description                       |
|-----------------|---------------------|-----------------------------------|
| floorId         | String              | Unique floor ID                   |
| name            | String              | Floor name                        |
| shortName       | String              | Short name                        |
| elevation       | Integer             | Elevation                         |
| siteId          | String              | Associated site ID                |
| buildingId      | String              | Associated building ID            |

### locations
- **Model:** `Location`
- **Purpose:** Represents locations (tenants, amenities, etc.) within a site.

| Field                       | Type                        | Description                       |
|-----------------------------|-----------------------------|-----------------------------------|
| locationId                  | String                      | Unique location ID                |
| name                        | String                      | Location name                     |
| type                        | Type (enum)                 | Location type                     |
| description                 | String                      | Description                       |
| hidden                      | boolean                     | Is hidden                         |
| amenity                     | Amenity (enum)              | Amenity type                      |
| phone                       | HashMap<String, String>     | Phone numbers                     |
| social                      | HashMap<String, String>     | Social links                      |
| operationHours              | List<OperationHour>         | Operation hours                   |
| links                       | List<Link>                  | Related links                     |
| address                     | String                      | Address                           |
| siteId                      | String                      | Associated site ID                |
| node                        | Node (DBRef)                | Node reference                    |
| categories                  | List<String>                | Categories                        |
| tags                        | List<String>                | Tags                              |

### nodes
- **Model:** `Node`
- **Purpose:** Represents nodes in the navigation graph.

| Field           | Type           | Description                       |
|-----------------|----------------|-----------------------------------|
| nodeId          | String         | Unique node ID                    |
| lat             | double         | Latitude                          |
| lng             | double         | Longitude                         |
| accessible      | boolean        | Is accessible                     |
| privateNode     | boolean        | Is private node                   |
| floorId         | String         | Associated floor ID               |
| siteId          | String         | Associated site ID                |

### sites
- **Model:** `Site`
- **Purpose:** Represents sites (e.g., malls, campuses) with their settings and metadata.

| Field           | Type                    | Description                       |
|-----------------|-------------------------|-----------------------------------|
| siteId          | String                  | Unique site ID                    |
| siteName        | String                  | Site name                         |
| type            | Type (enum)             | Site type                         |
| link            | String                  | Site link                         |
| operationHours  | List<OperationHour>     | Operation hours                   |
| latitude        | double                  | Latitude                          |
| longitude       | double                  | Longitude                         |
| address         | String                  | Address                           |
| city            | String                  | City                              |
| countryCode     | String                  | Country code                      |
| postal          | String                  | Postal code                       |
| state           | String                  | State                             |
| telephone       | String                  | Telephone                         |
| topLocations    | List<String>            | Top locations                     |
| tzId            | String                  | Timezone ID                       |
| utcOffset       | String                  | UTC offset                        |
| draft           | boolean                 | Is draft                          |
| archived        | boolean                 | Is archived                       |

---

*This document was generated based on the Java model classes in `src/main/java/com/beco/backend/model/document/`.*
