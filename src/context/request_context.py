from contextvars import ContextVar
from typing import Any


class RequestContextManager:
    _vars: dict[str, ContextVar[Any]] = {}

    @classmethod
    def set(cls, key: str, value: Any) -> None:
        if key not in cls._vars:
            cls._vars[key] = ContextVar(key, default=None)
        cls._vars[key].set(value)

    @classmethod
    def get(cls, key: str) -> Any:
        var = cls._vars.get(key)
        if var is None:
            return None
        return var.get()

    @classmethod
    def clear(cls) -> None:
        for var in cls._vars.values():
            var.set(None)
