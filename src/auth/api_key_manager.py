import logging
from typing import Annotated

from fastapi import Depends, HTTPException, status

from src.constants.headers import HTTP_HEADER_API_KEY
from src.constants.request_context import (
    REQUEST_API_KEY,
    REQUEST_RATE_LIMIT_MAX_ATTEMPTS,
    REQUEST_RATE_LIMIT_REMAINING_ATTEMPTS,
    REQUEST_RATE_LIMIT_RETRY_AFTER,
    REQUEST_SITE_ID,
)
from src.context.request_context import RequestContextManager
from src.extensions.ext_redis import redis_client
from src.models.entities.api_key import Api<PERSON>ey
from src.utils.rate_limiter import get_api_key_rate_limiter
from src.utils.redis_utility import RedisService

log = logging.getLogger(__name__)


class ApiKeyManager:
    def __init__(self, app: None = None):
        self.app = app

    async def authenticate(self, request):
        """
        Verifies the API key from the request headers using Beanie ODM.
        Sets per-request context (e.g., site_id, api_key) if valid.

        Returns:
            True if login succeeds; raises HTTPException otherwise.
        """

        log.info("Request received for API key authentication")

        # Extract API key from request header
        api_key_hash = request.headers.get(HTTP_HEADER_API_KEY)

        # if no api key, return 401
        if not api_key_hash:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="API key missing")

        redis_service = RedisService(redis_client)

        # Cache key for API key object
        cache_key = f"apikey:{api_key_hash}"

        # Try to fetch API key object from Redis and always rehydrate as model
        cached_api_key = redis_service.get_cache(cache_key)
        if cached_api_key:
            try:
                api_key = ApiKey.model_validate_json(cached_api_key)
                log.info("API key fetched from Redis cache.")
            except Exception as e:
                log.warning("Corrupted API key cache. Deleting cache entry.")
                redis_service.delete_cache(cache_key)
                log.info("Deleted corrupted cache for API key.")
                api_key = None
        else:
            api_key = None

        # If not found in cache, fetch from DB and cache it
        if not api_key:
            api_key = await ApiKey.find_one(ApiKey.api_key_hash == api_key_hash)
            if not api_key:
                log.info("API key not found in DB.")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid API key")
            redis_service.set_cache(cache_key, api_key.to_json(), expire_seconds=3600)
            log.info("API key fetched from DB and cached in Redis.")

        # Block disabled API keys before rate limiting
        if api_key.disabled:
            log.warning("API Key is disabled")
            raise HTTPException(status_code=403, detail="API key is disabled")

        # get rate limiter
        rate_limiter = get_api_key_rate_limiter()
        RequestContextManager.set(REQUEST_RATE_LIMIT_MAX_ATTEMPTS, rate_limiter.max_attempts)

        # Check rate limiting before any authentication attempts
        if rate_limiter.is_rate_limited(api_key_hash, redis_client):
            # Get retry time and remaining attempts
            retry_after = rate_limiter.get_time_until_reset(api_key_hash, redis_client)
            remaining_attempts = rate_limiter.get_remaining_attempts(api_key_hash, redis_client)

            # Set in request context for middleware to add headers
            RequestContextManager.set(REQUEST_RATE_LIMIT_REMAINING_ATTEMPTS, remaining_attempts)
            RequestContextManager.set(REQUEST_RATE_LIMIT_RETRY_AFTER, retry_after)
            raise HTTPException(status_code=status.HTTP_429_TOO_MANY_REQUESTS, detail="API key rate limit exceeded")

        # Increment rate limit first
        rate_limiter.increment_rate_limit(api_key_hash, redis_client)

        # Then get accurate remaining attempts (no retry_after needed for success)
        remaining_attempts = rate_limiter.get_remaining_attempts(api_key_hash, redis_client)
        RequestContextManager.set(REQUEST_RATE_LIMIT_REMAINING_ATTEMPTS, remaining_attempts)

        # Set API key and site ID in request context for downstream use
        RequestContextManager.set(REQUEST_API_KEY, api_key_hash)
        RequestContextManager.set(REQUEST_SITE_ID, api_key.site_id)

        log.info("API key authentication succeeded.")
        return True


def get_api_key_manager() -> ApiKeyManager:
    return ApiKeyManager()


ApiKeyManagerDep = Annotated[ApiKeyManager, Depends(get_api_key_manager)]
