from ..beco_app import <PERSON><PERSON>App
from ..configs import mcp_agent_config


def is_enabled() -> bool:
    return bool(mcp_agent_config.MONGO_URI)


class MongoClientWrapper:
    """
    A wrapper class for the MongoDB client that allows deferred initialization.
    This is useful in scenarios where the MongoDB client may need to be re-initialized,
    such as during configuration reloads or connection changes.

    Attributes:
        _client (AsyncIOMotorClient): The actual MongoDB client instance. It remains None until
                                      initialized with the `initialize` method.

    Methods:
        initialize(client): Initializes the MongoDB client if it hasn't been initialized already.
        __getattr__(item): Delegates attribute access to the MongoDB client, raising an error
                           if the client is not initialized.
    """

    def __init__(self):
        self._client = None

    def initialize(self, client):
        if self._client is None:
            self._client = client

    def __getattr__(self, item):
        if self._client is None:
            raise RuntimeError("MongoDB client is not initialized. Call init_app first.")
        return getattr(self._client, item)

    def __del__(self):
        self.close()

    def close(self):
        if self._client is not None:
            self._client.close()
            self._client = None


mongo_client = MongoClientWrapper()


def init_app(app: BecoApp):
    import asyncio

    from beanie import init_beanie
    from motor.motor_asyncio import AsyncIOMotorClient

    from src.models.category import CategoryInfo
    from src.models.entities.api_key import ApiKey
    from src.models.floor import FloorInfo
    from src.models.location import LocationInfo
    from src.models.node import NodeInfo
    from src.models.sites import SiteInfo

    client = AsyncIOMotorClient(mcp_agent_config.MONGO_URI)
    mongo_client.initialize(client)
    app.state.mongo_client = mongo_client
    app.state.mongo_db = mongo_client._client[mcp_agent_config.MONGO_DB_NAME]

    async def beanie_init():
        await init_beanie(
            database=client[mcp_agent_config.MONGO_DB_NAME],
            document_models=[SiteInfo, FloorInfo, LocationInfo, NodeInfo, CategoryInfo, ApiKey],
        )

    loop = asyncio.get_event_loop()
    loop.create_task(beanie_init())
