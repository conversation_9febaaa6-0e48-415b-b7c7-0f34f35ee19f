"""Security extension for the Beco MCP Server.

This extension provides comprehensive security features including:
- Security headers middleware
- Security configuration validation
- Security logging and monitoring
- Security information retrieval
"""

import logging
from typing import Any

from ..beco_app import <PERSON>co<PERSON>pp
from ..configs import mcp_agent_config
from ..middleware.security_headers_middleware import SecurityHeadersMiddleware
from ..utils.security_utils import get_security_info, log_security_config, validate_security_config

log = logging.getLogger(__name__)


def init_app(app: BecoApp) -> None:
    """Initialize the security extension with the FastAPI application.

    This function:
    1. Creates security configuration from environment
    2. Validates the security configuration
    3. Logs the security configuration details
    4. Adds the security headers middleware
    5. Stores security info in app state

    Args:
        app: The BecoApp (FastAPI) instance
    """
    # Check if security is enabled
    if not is_enabled():
        log.info("Security extension is disabled by configuration")
        return

    try:
        # Create security configuration from environment config
        security_config = _create_security_config_from_env(mcp_agent_config)

        # Validate the security configuration
        validation_result = validate_security_config(security_config)
        if not validation_result["is_valid"]:
            log.warning(f"Security configuration validation failed: {validation_result['errors']}")
            # Continue with default configuration but log warnings

        # Log security configuration
        log_security_config(security_config)

        # Add security headers middleware if enabled
        if mcp_agent_config.SECURITY_HEADERS_ENABLED:
            app.add_middleware(SecurityHeadersMiddleware, config=security_config)
            log.info("Security headers middleware added")
        else:
            log.info("Security headers middleware is disabled by configuration")

        # Store security configuration in app state for runtime access
        app.state.security_config = security_config
        app.state.security_info = get_security_info(security_config)

        # Create security endpoints
        _register_security_endpoints(app)

        log.info("Security extension initialized successfully")

    except Exception:
        log.exception("Failed to initialize security extension")
        # In production, you might want to fail fast here
        # For now, we'll continue without security headers
        log.warning("Continuing without security headers due to initialization failure")


def is_enabled() -> bool:
    """Check if the security extension is enabled.

    Returns:
        bool: True if security extension should be enabled
    """
    return getattr(mcp_agent_config, "SECURITY_ENABLED", True)


def _create_security_config_from_env(env_config) -> dict[str, Any]:
    """Create security configuration dict from environment configuration.

    Args:
        env_config: The environment configuration object

    Returns:
        Dict containing security configuration
    """
    config = {}

    # Build configuration from different categories
    _add_csp_config_from_env(config, env_config)
    _add_hsts_config_from_env(config, env_config)
    _add_header_config_from_env(config, env_config)
    _add_cross_origin_config_from_env(config, env_config)
    _add_server_config_from_env(config, env_config)
    _add_proxy_config_from_env(config, env_config)

    return config


def _add_csp_config_from_env(config: dict[str, Any], env_config) -> None:
    """Add CSP-related configuration from environment."""
    if hasattr(env_config, "CSP_ENABLED"):
        config["enable_csp"] = env_config.CSP_ENABLED
    if hasattr(env_config, "CSP_REPORT_ONLY"):
        config["csp_report_only"] = env_config.CSP_REPORT_ONLY
    if hasattr(env_config, "CSP_REPORT_URI") and env_config.CSP_REPORT_URI:
        config["csp_report_uri"] = env_config.CSP_REPORT_URI


def _add_hsts_config_from_env(config: dict[str, Any], env_config) -> None:
    """Add HSTS-related configuration from environment."""
    if hasattr(env_config, "HSTS_ENABLED"):
        config["enable_hsts"] = env_config.HSTS_ENABLED
    if hasattr(env_config, "HSTS_MAX_AGE"):
        config["hsts_max_age"] = env_config.HSTS_MAX_AGE
    if hasattr(env_config, "HSTS_INCLUDE_SUBDOMAINS"):
        config["hsts_include_subdomains"] = env_config.HSTS_INCLUDE_SUBDOMAINS
    if hasattr(env_config, "HSTS_PRELOAD"):
        config["hsts_preload"] = env_config.HSTS_PRELOAD


def _add_header_config_from_env(config: dict[str, Any], env_config) -> None:
    """Add basic security header configuration from environment."""
    if hasattr(env_config, "FRAME_OPTIONS_ENABLED"):
        config["enable_frame_options"] = env_config.FRAME_OPTIONS_ENABLED
    if hasattr(env_config, "FRAME_OPTIONS_VALUE"):
        config["frame_options"] = env_config.FRAME_OPTIONS_VALUE
    if hasattr(env_config, "CONTENT_TYPE_OPTIONS_ENABLED"):
        config["enable_content_type_options"] = env_config.CONTENT_TYPE_OPTIONS_ENABLED
    if hasattr(env_config, "XSS_PROTECTION_ENABLED"):
        config["enable_xss_protection"] = env_config.XSS_PROTECTION_ENABLED
    if hasattr(env_config, "REFERRER_POLICY_ENABLED"):
        config["enable_referrer_policy"] = env_config.REFERRER_POLICY_ENABLED
    if hasattr(env_config, "PERMISSIONS_POLICY_ENABLED"):
        config["enable_permissions_policy"] = env_config.PERMISSIONS_POLICY_ENABLED


def _add_cross_origin_config_from_env(config: dict[str, Any], env_config) -> None:
    """Add cross-origin policy configuration from environment."""
    if hasattr(env_config, "COEP_ENABLED"):
        config["enable_coep"] = env_config.COEP_ENABLED
    if hasattr(env_config, "COOP_ENABLED"):
        config["enable_coop"] = env_config.COOP_ENABLED
    if hasattr(env_config, "CORP_ENABLED"):
        config["enable_corp"] = env_config.CORP_ENABLED


def _add_server_config_from_env(config: dict[str, Any], env_config) -> None:
    """Add server header configuration from environment."""
    if hasattr(env_config, "HIDE_SERVER_HEADER"):
        config["hide_server_header"] = env_config.HIDE_SERVER_HEADER
    if hasattr(env_config, "CUSTOM_SERVER_HEADER") and env_config.CUSTOM_SERVER_HEADER:
        config["custom_server_header"] = env_config.CUSTOM_SERVER_HEADER


def _add_proxy_config_from_env(config: dict[str, Any], env_config) -> None:
    """Add proxy header trust configuration from environment."""
    if hasattr(env_config, "TRUST_PROXY_HEADERS"):
        config["trust_proxy_headers"] = env_config.TRUST_PROXY_HEADERS


def _register_security_endpoints(app: BecoApp) -> None:
    """Register security-related endpoints.

    Args:
        app: The BecoApp (FastAPI) instance
    """
    from fastapi import APIRouter
    from fastapi.responses import JSONResponse

    security_router = APIRouter(prefix="/security", tags=["security"])

    @security_router.get("/info")
    async def get_security_status():
        """Get current security configuration and status."""
        try:
            if hasattr(app.state, "security_info"):
                return app.state.security_info
            else:
                return {"error": "Security extension not initialized"}
        except Exception:
            log.exception("Error getting security info")
            return JSONResponse(status_code=500, content={"error": "Failed to retrieve security information"})

    @security_router.get("/headers")
    async def get_security_headers():
        """Get the security headers that would be applied."""
        try:
            if hasattr(app.state, "security_config"):
                config = app.state.security_config
                # Create a temporary middleware instance to get headers
                from ..middleware.security_headers_middleware import SecurityHeadersMiddleware

                middleware = SecurityHeadersMiddleware(None, config)
                return {"headers": middleware.get_applied_headers()}
            else:
                return {"error": "Security extension not initialized"}
        except Exception:
            log.exception("Error getting security headers")
            return JSONResponse(status_code=500, content={"error": "Failed to retrieve security headers"})

    # Only add endpoints in development mode
    if mcp_agent_config.DEBUG:
        app.include_router(security_router)
        log.info("Security endpoints registered (development mode only)")


# Export the main components for external use
__all__ = ["init_app", "is_enabled"]
