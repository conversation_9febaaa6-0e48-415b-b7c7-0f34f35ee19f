"""Security utility functions for the Beco MCP Server.

This module provides utility functions for security configuration validation,
logging, and information retrieval.
"""

import logging
from datetime import UTC, datetime
from typing import Any
from urllib.parse import urlparse

log = logging.getLogger(__name__)


def validate_security_config(config: dict[str, Any]) -> dict[str, Any]:
    """Validate the security configuration for potential issues.

    This function performs comprehensive validation of the security configuration
    to identify potential security issues, misconfigurations, or best practice
    violations.

    Args:
        config: The security headers configuration to validate

    Returns:
        Dict containing validation results with keys:
        - is_valid: bool indicating if configuration is valid
        - errors: List of error messages
        - warnings: List of warning messages
        - recommendations: List of security recommendations
    """
    try:
        validation_results = _collect_all_validation_results(config)
        return _build_validation_response(validation_results)
    except Exception:
        log.exception("Error during security config validation")
        return _build_error_response("Validation process failed")


def _collect_all_validation_results(config: dict[str, Any]) -> dict[str, list[str]]:
    """Collect validation results from all security components."""
    results = {"errors": [], "warnings": [], "recommendations": []}

    # Run all validation functions
    validation_functions = [
        _validate_csp_config,
        _validate_hsts_config,
        _validate_frame_options,
        _validate_permissions_policy,
        _validate_cors_policies,
    ]

    for validate_func in validation_functions:
        validation_result = validate_func(config)
        _merge_validation_results(results, validation_result)

    # Add general recommendations
    general_recommendations = _get_general_security_recommendations(config)
    results["recommendations"].extend(general_recommendations)

    return results


def _merge_validation_results(target: dict[str, list[str]], source: dict[str, list[str]]) -> None:
    """Merge validation results from source into target."""
    for key in ["errors", "warnings", "recommendations"]:
        if key in source:
            target[key].extend(source[key])


def _build_validation_response(results: dict[str, list[str]]) -> dict[str, Any]:
    """Build the final validation response."""
    return {
        "is_valid": len(results["errors"]) == 0,
        "errors": results["errors"],
        "warnings": results["warnings"],
        "recommendations": results["recommendations"],
        "validation_timestamp": datetime.now(UTC).isoformat(),
    }


def _build_error_response(error_message: str) -> dict[str, Any]:
    """Build an error response for validation failures."""
    return {
        "is_valid": False,
        "errors": [error_message],
        "warnings": [],
        "recommendations": [],
        "validation_timestamp": datetime.now(UTC).isoformat(),
    }


def _validate_csp_config(config: dict[str, Any]) -> dict[str, list[str]]:
    """Validate Content Security Policy configuration."""
    errors = []
    warnings = []
    recommendations = []

    # Early return if CSP is disabled
    if not config.get("enable_csp", True):
        warnings.append("Content Security Policy is disabled - consider enabling for better security")
        return {"errors": errors, "warnings": warnings, "recommendations": recommendations}

    # Check CSP directives
    csp_directives = config.get("csp_directives", {})
    warnings.extend(_check_csp_directive_sources(csp_directives))
    recommendations.extend(_check_missing_csp_directives(csp_directives))

    # Validate report URI
    csp_report_uri = config.get("csp_report_uri")
    if csp_report_uri and not _is_valid_url(csp_report_uri):
        errors.append(f"Invalid CSP report URI: {csp_report_uri}")

    return {"errors": errors, "warnings": warnings, "recommendations": recommendations}


def _check_csp_directive_sources(csp_directives: dict[str, list[str]]) -> list[str]:
    """Check CSP directive sources for unsafe values."""
    warnings = []

    for directive, sources in csp_directives.items():
        for source in sources:
            if source == "'unsafe-eval'":
                warnings.append(f"CSP directive {directive} contains 'unsafe-eval' - security risk")
            elif source == "'unsafe-inline'" and directive in ["script-src", "style-src"]:
                warnings.append(f"CSP directive {directive} contains 'unsafe-inline' - consider using nonces or hashes")
            elif source == "*":
                warnings.append(f"CSP directive {directive} allows all sources (*) - too permissive")

    return warnings


def _check_missing_csp_directives(csp_directives: dict[str, list[str]]) -> list[str]:
    """Check for missing important CSP directives."""
    recommendations = []
    important_directives = ["default-src", "script-src", "object-src", "base-uri"]

    for directive in important_directives:
        if directive not in csp_directives:
            recommendations.append(f"Consider adding CSP directive {directive} for better security")

    return recommendations


def _validate_hsts_config(config: dict[str, Any]) -> dict[str, list[str]]:
    """Validate HSTS configuration."""
    errors = []
    warnings = []
    recommendations = []

    if not config.get("enable_hsts", True):
        warnings.append("HSTS is disabled - enable for HTTPS sites to prevent downgrade attacks")
        return {"errors": errors, "warnings": warnings, "recommendations": recommendations}

    # Check max-age value
    hsts_max_age = config.get("hsts_max_age", 31536000)
    if hsts_max_age < 300:  # 5 minutes
        warnings.append(f"HSTS max-age of {hsts_max_age} seconds is very short")
    elif hsts_max_age < 86400:  # 1 day
        recommendations.append("Consider increasing HSTS max-age to at least 1 day (86400 seconds)")

    # Check preload requirements
    if config.get("hsts_preload", False):
        if hsts_max_age < 31536000:  # 1 year
            errors.append("HSTS preload requires max-age of at least 1 year (31536000 seconds)")
        if not config.get("hsts_include_subdomains", True):
            errors.append("HSTS preload requires includeSubDomains to be enabled")

    return {"errors": errors, "warnings": warnings, "recommendations": recommendations}


def _validate_frame_options(config: dict[str, Any]) -> dict[str, list[str]]:
    """Validate X-Frame-Options configuration."""
    errors = []
    warnings = []

    if not config.get("enable_frame_options", True):
        warnings.append("X-Frame-Options is disabled - consider enabling to prevent clickjacking")
        return {"errors": errors, "warnings": warnings}

    frame_options = config.get("frame_options", "DENY")
    valid_options = ["DENY", "SAMEORIGIN"]
    if frame_options not in valid_options and not frame_options.startswith("ALLOW-FROM "):
        errors.append(f"Invalid X-Frame-Options value: {frame_options}")

    if frame_options.startswith("ALLOW-FROM "):
        uri = frame_options[11:]  # Remove "ALLOW-FROM "
        if not _is_valid_url(uri):
            errors.append(f"Invalid URI in X-Frame-Options ALLOW-FROM: {uri}")

    return {"errors": errors, "warnings": warnings}


def _validate_permissions_policy(config: dict[str, Any]) -> dict[str, list[str]]:
    """Validate Permissions Policy configuration."""
    errors = []
    warnings = []

    if not config.get("enable_permissions_policy", True):
        warnings.append("Permissions Policy is disabled - consider enabling to control feature access")
        return {"errors": errors, "warnings": warnings}

    # Check for overly permissive policies
    permissions_policy = config.get("permissions_policy", {})
    for feature, allowlist in permissions_policy.items():
        if "*" in allowlist:
            warnings.append(f"Permissions Policy for {feature} allows all origins (*) - too permissive")

    return {"errors": errors, "warnings": warnings}


def _validate_cors_policies(config: dict[str, Any]) -> dict[str, list[str]]:
    """Validate Cross-Origin policies."""
    warnings = []
    recommendations = []

    # Check COEP
    if not config.get("enable_coep", False):
        recommendations.append("Consider enabling Cross-Origin-Embedder-Policy for better isolation")

    # Check COOP
    if not config.get("enable_coop", True):
        recommendations.append("Consider enabling Cross-Origin-Opener-Policy for better isolation")

    # Check CORP
    if not config.get("enable_corp", True):
        recommendations.append("Consider enabling Cross-Origin-Resource-Policy for better resource protection")

    return {"warnings": warnings, "recommendations": recommendations}


def _get_general_security_recommendations(config: dict[str, Any]) -> list[str]:
    """Get general security recommendations."""
    recommendations = []

    if not config.get("hide_server_header", True):
        recommendations.append("Consider hiding or customizing the Server header to reduce information disclosure")

    if not config.get("custom_headers", {}):
        recommendations.append("Consider adding custom security headers specific to your application needs")

    return recommendations


def _is_valid_url(url: str) -> bool:
    """Check if a URL is valid."""
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
    except Exception:
        return False


def log_security_config(config: dict[str, Any]) -> None:
    """Log the security configuration details.

    This function logs comprehensive information about the current security
    configuration for auditing and debugging purposes.

    Args:
        config: The security headers configuration to log
    """
    try:
        log.info("=== Security Configuration Summary ===")

        # Log CSP configuration
        if config.get("enable_csp", True):
            csp_report_only = config.get("csp_report_only", False)
            log.info(f"Content Security Policy: {'Report-Only' if csp_report_only else 'Enforced'}")
            csp_report_uri = config.get("csp_report_uri")
            if csp_report_uri:
                log.info(f"CSP Report URI: {csp_report_uri}")
        else:
            log.warning("Content Security Policy: DISABLED")

        # Log HSTS configuration
        if config.get("enable_hsts", True):
            hsts_max_age = config.get("hsts_max_age", 31536000)
            hsts_include_subdomains = config.get("hsts_include_subdomains", True)
            hsts_preload = config.get("hsts_preload", False)

            hsts_value = f"max-age={hsts_max_age}"
            if hsts_include_subdomains:
                hsts_value += "; includeSubDomains"
            if hsts_preload:
                hsts_value += "; preload"

            log.info(f"HSTS Header: {hsts_value}")
        else:
            log.warning("HTTP Strict Transport Security: DISABLED")

        # Log other security headers
        security_headers = []

        if config.get("enable_frame_options", True):
            frame_options = config.get("frame_options", "DENY")
            security_headers.append(f"X-Frame-Options: {frame_options}")

        if config.get("enable_content_type_options", True):
            security_headers.append("X-Content-Type-Options: nosniff")

        if config.get("enable_xss_protection", True):
            xss_protection_value = config.get("xss_protection_value", "1; mode=block")
            security_headers.append(f"X-XSS-Protection: {xss_protection_value}")

        if config.get("enable_referrer_policy", True):
            referrer_policy = config.get("referrer_policy", "strict-origin-when-cross-origin")
            security_headers.append(f"Referrer-Policy: {referrer_policy}")

        if config.get("enable_coep", False):
            coep_value = config.get("coep_value", "require-corp")
            security_headers.append(f"Cross-Origin-Embedder-Policy: {coep_value}")

        if config.get("enable_coop", True):
            coop_value = config.get("coop_value", "same-origin")
            security_headers.append(f"Cross-Origin-Opener-Policy: {coop_value}")

        if config.get("enable_corp", True):
            corp_value = config.get("corp_value", "same-origin")
            security_headers.append(f"Cross-Origin-Resource-Policy: {corp_value}")

        for header in security_headers:
            log.info(f"Security Header: {header}")

        # Log server header configuration
        if config.get("hide_server_header", True):
            custom_server_header = config.get("custom_server_header")
            if custom_server_header:
                log.info(f"Server Header: Custom ({custom_server_header})")
            else:
                log.info("Server Header: Hidden")
        else:
            log.info("Server Header: Default")

        # Log custom headers
        custom_headers = config.get("custom_headers", {})
        if custom_headers:
            log.info("Custom Security Headers:")
            for name, value in custom_headers.items():
                log.info(f"  {name}: {value}")

        log.info("=== End Security Configuration ===")

    except Exception as e:
        log.exception("Failed to log security configuration")


def get_security_info(config: dict[str, Any]) -> dict[str, Any]:
    """Get comprehensive security information and status.

    This function returns detailed information about the current security
    configuration, applied headers, and security status.

    Args:
        config: The security headers configuration

    Returns:
        Dict containing comprehensive security information
    """
    try:
        # Get validation results
        validation_result = validate_security_config(config)

        # Build applied headers (simplified version)
        applied_headers = {}

        if config.get("enable_csp", True):
            applied_headers["Content-Security-Policy"] = "enabled"

        if config.get("enable_hsts", True):
            applied_headers["Strict-Transport-Security"] = "enabled"

        if config.get("enable_frame_options", True):
            applied_headers["X-Frame-Options"] = config.get("frame_options", "DENY")

        if config.get("enable_content_type_options", True):
            applied_headers["X-Content-Type-Options"] = "nosniff"

        # Calculate security score
        security_score = _calculate_security_score(config, validation_result)

        return {
            "timestamp": datetime.now(UTC).isoformat(),
            "security_score": security_score,
            "validation": validation_result,
            "applied_headers": applied_headers,
            "configuration": {
                "csp_enabled": config.get("enable_csp", True),
                "hsts_enabled": config.get("enable_hsts", True),
                "frame_options_enabled": config.get("enable_frame_options", True),
                "content_type_options_enabled": config.get("enable_content_type_options", True),
                "xss_protection_enabled": config.get("enable_xss_protection", True),
                "referrer_policy_enabled": config.get("enable_referrer_policy", True),
                "permissions_policy_enabled": config.get("enable_permissions_policy", True),
                "coep_enabled": config.get("enable_coep", False),
                "coop_enabled": config.get("enable_coop", True),
                "corp_enabled": config.get("enable_corp", True),
                "server_header_hidden": config.get("hide_server_header", True),
                "custom_headers_count": len(config.get("custom_headers", {})),
            },
            "recommendations": validation_result.get("recommendations", []),
        }

    except Exception as e:
        log.exception("Failed to get security info")
        return {"timestamp": datetime.now(UTC).isoformat(), "error": str(e), "security_score": 0}


def _calculate_security_score(config: dict[str, Any], validation_result: dict[str, Any]) -> int:
    """Calculate a security score based on configuration and validation results.

    Args:
        config: The security configuration
        validation_result: The validation results

    Returns:
        Security score from 0-100
    """
    score = 0

    # Base scores for enabled features
    if config.get("enable_csp", True):
        score += 25
    if config.get("enable_hsts", True):
        score += 20
    if config.get("enable_frame_options", True):
        score += 10
    if config.get("enable_content_type_options", True):
        score += 5
    if config.get("enable_xss_protection", True):
        score += 5
    if config.get("enable_referrer_policy", True):
        score += 5
    if config.get("enable_permissions_policy", True):
        score += 10
    if config.get("enable_coep", False):
        score += 5
    if config.get("enable_coop", True):
        score += 5
    if config.get("enable_corp", True):
        score += 5
    if config.get("hide_server_header", True):
        score += 5

    # Deduct points for errors and warnings
    error_count = len(validation_result.get("errors", []))
    warning_count = len(validation_result.get("warnings", []))

    score -= error_count * 10  # 10 points per error
    score -= warning_count * 5  # 5 points per warning

    # Ensure score is within bounds
    return max(0, min(score, 100))
