from pydantic import Field
from pydantic_settings import BaseSettings


class SecurityConfig(BaseSettings):
    """
    Security-related configurations for the application
    """

    SECRET_KEY: str = Field(
        description="Secret key for secure session cookie signing."
        "Make sure you are changing this key for your deployment with a strong key."
        "Generate a strong key using `openssl rand -base64 42` or set via the `SECRET_KEY` environment variable.",
        default="",
    )

    # Security Extension Configuration
    SECURITY_ENABLED: bool = Field(
        description="Enable or disable the security extension",
        default=True,
    )

    SECURITY_HEADERS_ENABLED: bool = Field(
        description="Enable or disable security headers middleware",
        default=True,
    )

    # Content Security Policy
    CSP_ENABLED: bool = Field(
        description="Enable Content Security Policy header",
        default=True,
    )

    CSP_REPORT_ONLY: bool = Field(
        description="Use CSP in report-only mode",
        default=False,
    )

    CSP_REPORT_URI: str = Field(
        description="URI for CSP violation reports",
        default="",
    )

    # HTTP Strict Transport Security
    HSTS_ENABLED: bool = Field(
        description="Enable HTTP Strict Transport Security",
        default=True,
    )

    HSTS_MAX_AGE: int = Field(
        description="HSTS max-age in seconds",
        default=31536000,  # 1 year
        ge=0,
    )

    HSTS_INCLUDE_SUBDOMAINS: bool = Field(
        description="Include subdomains in HSTS",
        default=True,
    )

    HSTS_PRELOAD: bool = Field(
        description="Enable HSTS preload",
        default=False,
    )

    # X-Frame-Options
    FRAME_OPTIONS_ENABLED: bool = Field(
        description="Enable X-Frame-Options header",
        default=True,
    )

    FRAME_OPTIONS_VALUE: str = Field(
        description="X-Frame-Options value (DENY, SAMEORIGIN, or ALLOW-FROM uri)",
        default="DENY",
    )

    # Other security headers
    CONTENT_TYPE_OPTIONS_ENABLED: bool = Field(
        description="Enable X-Content-Type-Options header",
        default=True,
    )

    XSS_PROTECTION_ENABLED: bool = Field(
        description="Enable X-XSS-Protection header",
        default=True,
    )

    REFERRER_POLICY_ENABLED: bool = Field(
        description="Enable Referrer-Policy header",
        default=True,
    )

    PERMISSIONS_POLICY_ENABLED: bool = Field(
        description="Enable Permissions-Policy header",
        default=True,
    )

    # Cross-Origin policies
    COEP_ENABLED: bool = Field(
        description="Enable Cross-Origin-Embedder-Policy header",
        default=False,
    )

    COOP_ENABLED: bool = Field(
        description="Enable Cross-Origin-Opener-Policy header",
        default=True,
    )

    CORP_ENABLED: bool = Field(
        description="Enable Cross-Origin-Resource-Policy header",
        default=True,
    )

    # Server header
    HIDE_SERVER_HEADER: bool = Field(
        description="Hide or modify the Server header",
        default=True,
    )

    CUSTOM_SERVER_HEADER: str = Field(
        description="Custom Server header value",
        default="",
    )

    # Proxy header trust settings
    TRUST_PROXY_HEADERS: bool = Field(
        description="Trust proxy headers (X-Forwarded-Proto, etc.) for HTTPS detection",
        default=False,
    )

    # Rate Limiting Configuration
    RATE_LIMIT_ENABLED: bool = Field(
        description="Enable or disable rate limiting",
        default=True,
    )

    RATE_LIMIT_API_KEY_MAX_ATTEMPTS: int = Field(
        description="Maximum number of API requests allowed per time window",
        default=100,
        ge=1,
    )

    RATE_LIMIT_API_KEY_TIME_WINDOW: int = Field(
        description="Time window in seconds for API key rate limiting",
        default=3600,  # 1 hour
        ge=1,
    )
