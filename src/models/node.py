"""
Node information models for the Beco ecosystem.

A Node represents a spatial point within a Site, typically used for navigation
and floor-based location relationships.
"""

from pydantic import Field

from src.models.core.base_document_model import BaseDocumentModel


class NodeInfo(BaseDocumentModel):
    """
    Node information model based on MongoDB schema.

    Matches the exact field structure from README_MONGO_COLLECTIONS.md
    """

    site_id: str = Field(..., alias="siteId", description="Associated site ID")
    floor_id: str = Field(..., alias="floorId", description="Associated floor ID")

    # Spatial information
    lat: float = Field(..., description="Latitude")
    lng: float = Field(..., description="Longitude")

    # Node properties
    accessible: bool = Field(default=True, description="Is accessible")
    private_node: bool = Field(default=False, alias="privateNode", description="Is private node")

    class Settings(BaseDocumentModel.Settings):
        name = "nodes"  # MongoDB collection name
