"""
Category information models for the Beco ecosystem.

A Category represents a classification system for locations and events within a Site.
"""

from pydantic import Field

from src.models.core.base_document_model import BaseDocumentModel


class CategoryInfo(BaseDocumentModel):
    """
    Category information model based on MongoDB schema.

    Represents a category classification for locations and events within a site.
    """

    site_id: str = Field(..., alias="siteId", description="Associated site ID")

    # Category information
    name: str = Field(..., description="Category name")

    class Settings(BaseDocumentModel.Settings):
        name = "categories"  # MongoDB collection name
