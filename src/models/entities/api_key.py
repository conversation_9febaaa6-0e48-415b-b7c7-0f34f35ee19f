from datetime import datetime

from pydantic import Field

from src.models.core.base_document_model import BaseDocumentModel


class ApiKey(BaseDocumentModel):
    name: str
    api_key_hash: str = Field(..., alias="apiKeyHash")
    disabled: bool
    site_id: str = Field(..., alias="siteId")
    created_by: str = Field(..., alias="createdBy")
    created_date_time: datetime = Field(..., alias="createdDateTime")
    updated_date_time: datetime = Field(..., alias="updatedDateTime")
    last_used: datetime | None = Field(None, alias="lastUsed")

    class Settings(BaseDocumentModel.Settings):
        name = "api_keys"  # MongoDB collection name
