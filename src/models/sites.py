"""
Site information models for the Beco ecosystem.

A Site is the core entity in the Beco ecosystem, representing a venue such as a mall, airport, university, or office.
"""

from __future__ import annotations

from pydantic import BaseModel, Field, HttpUrl

from src.models.core.base_document_model import BaseDocumentModel


class OperationHours(BaseModel):
    """Operation hours model for a site."""

    opens: str = Field(..., alias="opens", description="Opening time in HH:MM format")
    closes: str = Field(..., alias="closes", description="Closing time in HH:MM format")
    day_of_week: list[str] = Field(alias="dayOfWeek", description="List of days of the week")


class SiteInfo(BaseDocumentModel):
    """
    Site information response model.

    A Site is a core venue in the Beco ecosystem. It can represent a mall, airport,
    university, office, or similar large venue.
    """

    site_name: str = Field(..., alias="siteName", description="Name of the site")
    operation_hours: list[OperationHours] = Field(alias="operationHours", description="Operating hours for the site")
    latitude: float = Field(..., description="Latitude coordinate of the site")
    longitude: float = Field(..., description="Longitude coordinate of the site")
    address: str = Field(..., description="Full address of the site")
    city: str | None = Field(None, description="City where the site is located")
    country_code: str | None = Field(None, alias="countryCode", description="Country code")
    postal: str | None = Field(None, description="Postal code")
    state: str | None = Field(None, description="State or province")
    telephone: str = Field(..., description="Contact telephone number")
    top_locations: list[str] = Field(
        default_factory=list, alias="topLocations", description="Top locations within the site"
    )
    tz_id: str = Field(..., alias="tzId", description="Time zone identifier")
    utc_offset: str = Field(..., alias="utcOffset", description="UTC offset")
    link: HttpUrl = Field(..., description="Website link for the site")
    type: str = Field(..., description="Type of the site (e.g., MALL, AIRPORT, UNIVERSITY, OFFICE)")
    floors: list[FloorInfo] = Field(default_factory=list, description="Floors associated with this site")

    class Settings(BaseDocumentModel.Settings):
        name = "sites"  # MongoDB collection name


# Import FloorInfo after class definition to avoid circular imports
from src.models.floor import FloorInfo
