from beanie import Document, PydanticObjectId
from pydantic import AliasChoices, Field
from pydantic.alias_generators import to_snake


class BaseDocumentModel(Document):
    id: PydanticObjectId = Field(validation_alias=AliasChoices("_id", "id"))

    def to_json(self) -> str:
        """
        Convert the model to JSON string using proper aliases.
        This centralizes the model_dump configuration used across all services.
        """
        return self.model_dump_json(by_alias=True)

    class Settings:
        # Optionally set a default collection name or let child classes override
        name = None  # Child classes should set this
        use_state_management = True

    class Config:
        # Enable population by field name and alias, and camelCase aliasing
        populate_by_name = True
        alias_generator = to_snake
        allow_population_by_field_name = True
