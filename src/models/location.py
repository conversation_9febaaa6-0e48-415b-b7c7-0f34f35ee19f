"""
Location information models for the Beco ecosystem.

A Location represents a specific point of interest within a Site, such as a store,
amenity, or service location.
"""

from beanie import Link
from pydantic import Field

from src.models.core.base_document_model import BaseDocumentModel
from src.models.node import NodeInfo


class LocationInfo(BaseDocumentModel):
    """
    Location information model based on MongoDB schema.

    Represents a specific location within a site, such as stores, amenities,
    or points of interest.
    """

    site_id: str = Field(..., alias="siteId", description="Associated site ID")

    # Location information
    name: str = Field(..., description="Location name")
    description: str | None = Field(None, description="Location description")
    type: str | None = Field(None, description="Location type (TENANT, AMENITIES, PARKING, SEATING, GATE, etc.)")
    categories: list[str] = Field(
        default_factory=list, alias="categories", description="List of category IDs associated with this location"
    )
    node: Link[NodeInfo] | None = Field(None, description="Node reference (DBRef)", exclude=True)
    tags: list[str] = Field(default_factory=list, description="Location tags")
    amenity: str | None = Field(None, description="Amenity type (only for AMENITIES locations)")
    status: str = Field(default="active", description="Location status")

    class Settings(BaseDocumentModel.Settings):
        name = "locations"  # MongoDB collection name
