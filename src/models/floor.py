"""
Floor information models for the Beco ecosystem.

A Floor represents a level within a Site, used for spatial organization
and navigation.
"""

from pydantic import Field

from src.models.core.base_document_model import BaseDocumentModel


class FloorInfo(BaseDocumentModel):
    """
    Floor information model based on MongoDB schema.

    Matches the exact field structure from README_MONGO_COLLECTIONS.md
    """

    site_id: str = Field(..., alias="siteId", description="Associated site ID")
    building_id: str | None = Field(None, alias="buildingId", description="Associated building ID")

    # Floor information - make fields optional to handle incomplete data
    name: str | None = Field(None, description="Floor name")
    short_name: str | None = Field(None, alias="shortName", description="Short name")
    elevation: int | None = Field(None, description="Elevation")

    class Settings(BaseDocumentModel.Settings):
        name = "floors"  # MongoDB collection name
