"""Site information service."""

import logging
from typing import Annotated

from beanie import PydanticObjectId
from fastapi import Depends

from src.extensions.ext_redis import redis_client
from src.utils import is_valid_objectid

from ..exceptions import ResourceNotFoundError, ValidationError
from ..models.sites import SiteInfo
from .floors import FloorsService

log = logging.getLogger(__name__)


class SitesService:
    """Service for site information operations."""

    def __init__(self):
        """Initialize the sites service with floors service dependency."""
        self.floors_service = FloorsService()

    async def get_site_info(self, site_id: str) -> SiteInfo:
        """
        Fetch a site by its identifier using Beanie ODM, including associated floors.
        """
        if not site_id.strip() or not is_valid_objectid(site_id):
            raise ValidationError("Site ID must be provided")

        cache_key = f"siteinfo_with_floors:{site_id}"
        cached_response = redis_client.get(cache_key)
        if cached_response:
            log.info(f"Returning cached site info with floors for site_id: {site_id}")
            site_info = SiteInfo.model_validate_json(cached_response.decode())
            return site_info

        log.info(f"Fetching site info from database for site_id: {site_id}")
        site = await SiteInfo.get(PydanticObjectId(site_id))
        if site is None:
            raise ResourceNotFoundError(f"Site with ID '{site_id}' not found")

        # Fetch floors for this site
        log.info(f"Fetching floors for site_id: {site_id}")
        floors = await self.floors_service.get_floors_by_site_id(site_id)
        site.floors = floors

        # Cache the complete site info with floors
        redis_client.set(cache_key, site.to_json(), ex=3600)
        return site


def get_sites_service() -> SitesService:
    """Dependency to get sites service instance."""
    return SitesService()


# Type alias for dependency injection
SitesServiceDep = Annotated[SitesService, Depends(get_sites_service)]
