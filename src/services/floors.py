"""Floor information service."""

import json
import logging
from typing import Annotated

from fastapi import Depends

from src.extensions.ext_redis import redis_client
from src.utils import is_valid_objectid

from ..exceptions import ValidationError
from ..models.floor import FloorInfo

log = logging.getLogger(__name__)


class FloorsService:
    """Service for floor information operations."""

    async def get_floors_by_site_id(self, site_id: str) -> list[FloorInfo]:
        """
        Fetch all floors for a given site using Beanie ODM with caching.

        Args:
            site_id: The site identifier to query floors for

        Returns:
            List of FloorInfo objects for the site

        Raises:
            ValidationError: If site_id is invalid
        """
        if not site_id.strip():
            raise ValidationError("Site ID must be provided")

        if not is_valid_objectid(site_id):
            raise ValidationError("Site ID must be a valid ObjectId")

        cache_key = f"floors:{site_id}"
        cached_response = redis_client.get(cache_key)
        if cached_response:
            log.info(f"Returning cached floors for site_id: {site_id}")
            # Parse cached JSON array back to FloorInfo objects
            floors_data = json.loads(cached_response.decode())
            floors = [FloorInfo.model_validate(floor_data) for floor_data in floors_data]
            return floors

        log.info(f"Fetching floors from database for site_id: {site_id}")

        # Query floors by site_id using Beanie ODM
        # Use the MongoDB field name (alias) for the query
        floors = await FloorInfo.find({"siteId": site_id}).to_list()

        log.info(f"Found {len(floors)} floors for site_id: {site_id}")

        # Cache the floors data
        if floors:
            # Use centralized to_json() method from BaseDocumentModel
            floors_json = json.dumps([json.loads(floor.to_json()) for floor in floors])
            redis_client.set(cache_key, floors_json, ex=3600)  # Cache for 1 hour
        else:
            # Cache empty result for shorter time to allow for data updates
            redis_client.set(cache_key, "[]", ex=300)  # Cache for 5 minutes

        return floors


def get_floors_service() -> FloorsService:
    """Dependency to get floors service instance."""
    return FloorsService()


# Type alias for dependency injection
FloorsServiceDep = Annotated[FloorsService, Depends(get_floors_service)]
