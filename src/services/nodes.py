"""Node information service."""

import json
import logging
from typing import Annotated

from beanie import PydanticObjectId
from fastapi import Depends

from src.context.request_context import RequestContextManager
from src.extensions.ext_redis import redis_client
from src.utils import is_valid_objectid

from ..exceptions import ResourceNotFoundError, ValidationError
from ..models.node import NodeInfo

log = logging.getLogger(__name__)


class NodesService:
    """Service for node information operations."""

    async def get_node(self, node_id: str) -> NodeInfo:
        """
        Fetch a node by its identifier using Beanie ODM.

        Args:
            node_id: The node identifier to fetch

        Returns:
            NodeInfo object for the specified node

        Raises:
            ValidationError: If node_id is invalid
            ResourceNotFoundError: If node is not found or not accessible
        """
        if not node_id.strip() or not is_valid_objectid(node_id):
            raise ValidationError("Node ID must be provided and be a valid ObjectId")

        # TODO: Replace with authorization token from API header
        current_site_id = RequestContextManager.get("site_id")
        cache_key = f"node:{current_site_id}:{node_id}"
        cached_response = redis_client.get(cache_key)
        if cached_response:
            log.info(f"Returning cached node info for node_id: {node_id}")
            node_info = NodeInfo.model_validate_json(cached_response.decode())
            return node_info

        log.info(f"Fetching node info from database for node_id: {node_id}")

        # Optimize query to filter by both _id and siteId in single database operation
        node = await NodeInfo.find_one({"_id": PydanticObjectId(node_id), "siteId": current_site_id})
        if node is None:
            raise ResourceNotFoundError(f"Node with ID '{node_id}' not found")

        redis_client.set(cache_key, node.to_json(), ex=3600)  # Cache for 1 hour
        return node

    async def get_nodes(self, page: int = 1, limit: int = 10) -> list[NodeInfo]:
        """
        Fetch paginated nodes for the current site using Beanie ODM.

        Args:
            page: Page number (1-based)
            limit: Number of nodes per page (max 100)

        Returns:
            List of NodeInfo objects for the current page

        Raises:
            ValidationError: If pagination parameters are invalid
        """
        # Validate pagination parameters
        if page < 1:
            raise ValidationError("Page number must be greater than 0")

        if limit < 1 or limit > 100:
            raise ValidationError("Limit must be between 1 and 100")

        # TODO: Replace with authorization token from API header
        current_site_id = RequestContextManager.get("site_id")
        cache_key = f"nodes_page:{current_site_id}:{page}:{limit}"
        cached_response = redis_client.get(cache_key)
        if cached_response:
            log.info(f"Returning cached nodes for page {page}, limit {limit}")
            nodes_data = json.loads(cached_response.decode())
            nodes = [NodeInfo.model_validate(node_data) for node_data in nodes_data]
            return nodes

        log.info(f"Fetching nodes from database for page {page}, limit {limit}")

        # Calculate skip value for pagination
        skip = (page - 1) * limit

        # Query nodes with site_id filtering and pagination
        nodes = await NodeInfo.find({"siteId": current_site_id}).skip(skip).limit(limit).to_list()

        log.info(f"Found {len(nodes)} nodes for page {page}")

        # Cache the nodes data
        if nodes:
            nodes_json = json.dumps([json.loads(node.to_json()) for node in nodes])
            redis_client.set(cache_key, nodes_json, ex=3600)  # Cache for 1 hour
        else:
            # Cache empty result for shorter time to allow for data updates
            redis_client.set(cache_key, "[]", ex=300)  # Cache for 5 minutes

        return nodes


def get_nodes_service() -> NodesService:
    """Dependency to get nodes service instance."""
    return NodesService()


# Type alias for dependency injection
NodesServiceDep = Annotated[NodesService, Depends(get_nodes_service)]
