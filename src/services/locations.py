"""Location information service."""

import hashlib
import json
import logging
import re
from typing import Annotated

from beanie import PydanticObjectId
from fastapi import Depends

from src.context.request_context import RequestContextManager
from src.extensions.ext_redis import redis_client
from src.utils import is_valid_objectid

from ..exceptions import ResourceNotFoundError, ValidationError
from ..models.location import LocationInfo

log = logging.getLogger(__name__)


class LocationsService:
    """Service for location information operations."""

    async def get_location_by_id(self, location_id: str) -> LocationInfo:
        """
        Fetch a location by its identifier using Beanie ODM.
        """
        if not location_id.strip() or not is_valid_objectid(location_id):
            raise ValidationError("Location ID must be provided")

        current_site_id = RequestContextManager.get("site_id")
        cache_key = f"location:{current_site_id}:{location_id}"
        cached_response = redis_client.get(cache_key)
        if cached_response:
            log.info(f"Returning cached location info for location_id: {location_id}")
            location_info = LocationInfo.model_validate_json(cached_response.decode())
            return location_info

        log.info(f"Fetching location info from database for location_id: {location_id}")

        # Build query dictionary and use centralized query execution
        query = {"_id": PydanticObjectId(location_id)}
        locations = await self._execute_location_query(query)

        if not locations:
            raise ResourceNotFoundError(f"Location with ID '{location_id}' not found")

        location = locations[0]  # Get the first (and only) result
        redis_client.set(cache_key, location.to_json(), ex=3600)
        return location

    async def search_locations(
        self,
        floor_id: str | None = None,
        location_type: str | None = None,
        amenity_type: str | None = None,
        category_id: str | None = None,
        tags: list[str] | None = None,
        name: str | None = None,
    ) -> list[LocationInfo]:
        """
        Search for locations using flexible, combinable search criteria.
        At least one search parameter must be provided.

        Args:
            floor_id: Filter by specific floor ID through node relationship (exact match)
            location_type: Filter by location type (exact match)
            amenity_type: Filter by amenity type (exact match, only for AMENITIES locations)
            category_id: Filter by category ID (exact match)
            tags: Filter by services offered and location highlights (array, matches any)
            name: Filter by location name (case-insensitive contains/partial match)

        Returns:
            List of LocationInfo objects matching the search criteria

        Raises:
            ValidationError: If no search parameters provided or invalid ObjectIds
            ResourceNotFoundError: If no locations found matching criteria
        """
        # Validate that at least one search parameter is provided
        # Check for meaningful (non-empty, non-None) parameters
        valid_params = []
        if floor_id and floor_id.strip():
            valid_params.append(floor_id)
        if location_type and location_type.strip():
            valid_params.append(location_type)
        if amenity_type and amenity_type.strip():
            valid_params.append(amenity_type)
        if category_id and category_id.strip():
            valid_params.append(category_id)
        if tags and len(tags) > 0:
            valid_params.append(tags)
        if name and name.strip():
            valid_params.append(name)

        if not valid_params:
            raise ValidationError("At least one search parameter must be provided")

        # Validate ObjectId parameters
        if floor_id and (not floor_id.strip() or not is_valid_objectid(floor_id)):
            raise ValidationError("Floor ID must be a valid ObjectId")

        if category_id and (not category_id.strip() or not is_valid_objectid(category_id)):
            raise ValidationError("Category ID must be a valid ObjectId")

        # Validate other parameters
        if location_type and not location_type.strip():
            raise ValidationError("Location type must not be empty")

        if amenity_type and not amenity_type.strip():
            raise ValidationError("Amenity type must not be empty")

        if name and not name.strip():
            raise ValidationError("Name must not be empty")

        if tags and not all(tag.strip() for tag in tags):
            raise ValidationError("All service/highlight tags must be non-empty strings")

        # Get current site_id for cache key generation
        current_site_id = RequestContextManager.get("site_id")

        # Create cache key based on search parameters
        cache_key = self._generate_search_cache_key(
            current_site_id, floor_id, location_type, amenity_type, category_id, tags, name
        )

        # Check cache first
        cached_response = redis_client.get(cache_key)
        if cached_response:
            log.info(f"Returning cached location search results for parameters: {cache_key}")
            locations_data = json.loads(cached_response.decode())
            locations = [LocationInfo.model_validate(loc_data) for loc_data in locations_data]
            return locations

        log.info(
            f"Searching locations from database with parameters: floor_id={floor_id}, "
            f"location_type={location_type}, amenity_type={amenity_type}, "
            f"category_id={category_id}, tags={tags}, name={name}"
        )

        # Build MongoDB query (site_id will be automatically added by _execute_location_query)
        query = {}

        # Filter by floor_id through node relationship using dot notation
        if floor_id:
            query["node.floorId"] = floor_id

        if location_type:
            # Location type search: Uses contains search (substring matching)
            # Case-insensitive substring matching for better usability
            escaped_type = re.escape(location_type)
            regex_pattern = re.compile(escaped_type, re.IGNORECASE)
            query["type"] = {"$regex": regex_pattern}

        if amenity_type:
            # Amenity type search: Uses contains search (substring matching)
            # Case-insensitive substring matching for better usability
            escaped_amenity = re.escape(amenity_type)
            regex_pattern = re.compile(escaped_amenity, re.IGNORECASE)
            query["amenity"] = {"$regex": regex_pattern}

        if category_id:
            query["categories"] = category_id

        if tags:
            # Tags search: Returns locations that offer ANY of the specified services/highlights
            # Uses contains search (substring matching) within the tags array
            # Case-insensitive substring matching for better usability
            tag_conditions = []
            for tag in tags:
                # Create case-insensitive regex pattern for substring matching
                escaped_tag = re.escape(tag)
                regex_pattern = re.compile(escaped_tag, re.IGNORECASE)
                tag_conditions.append({"tags": {"$regex": regex_pattern}})

            # Use $or to match any tag that contains any of the search terms
            query["$or"] = tag_conditions

        if name:
            # Case-insensitive contains search using regex
            regex_pattern = re.compile(re.escape(name), re.IGNORECASE)
            query["name"] = {"$regex": regex_pattern}

        # Execute the search query with centralized query execution (site_id automatically added)
        locations = await self._execute_location_query(query, floor_id)

        if not locations:
            raise ResourceNotFoundError("No locations found matching the search criteria")

        # Cache the search results with optimized TTL
        search_params = {
            "floor_id": floor_id,
            "location_type": location_type,
            "amenity_type": amenity_type,
            "category_id": category_id,
            "tags": tags,
            "name": name,
        }
        cache_ttl = self._get_cache_ttl(search_params)

        locations_json = json.dumps([json.loads(loc.to_json()) for loc in locations])
        redis_client.set(cache_key, locations_json, ex=cache_ttl)

        log.info(f"Found {len(locations)} locations matching search criteria")
        return locations

    def _generate_search_cache_key(
        self,
        site_id: str,
        floor_id: str | None,
        location_type: str | None,
        amenity_type: str | None,
        category_id: str | None,
        tags: list[str] | None,
        name: str | None,
    ) -> str:
        """
        Generate optimized cache key for location search parameters with query normalization.

        This method creates cache keys that maximize cache hit rates by:
        1. Normalizing parameter values (case, whitespace, sorting)
        2. Using shorter, more readable cache keys for common patterns
        3. Grouping similar queries together

        Args:
            site_id: Current site ID
            floor_id: Floor ID parameter
            location_type: Location type parameter
            amenity_type: Amenity type parameter
            category_id: Category ID parameter
            tags: Tags list parameter
            name: Name search parameter

        Returns:
            Optimized cache key string
        """
        # For single-parameter searches, use optimized cache keys
        non_empty_params = sum(1 for param in [floor_id, location_type, amenity_type, category_id, tags, name] if param)

        if non_empty_params == 1:
            # Single parameter searches - use specific cache keys for better hit rates
            if floor_id:
                return f"loc_floor:{site_id}:{floor_id}"
            elif location_type:
                return f"loc_type:{site_id}:{location_type}"
            elif amenity_type:
                return f"loc_amenity:{site_id}:{amenity_type}"
            elif category_id:
                return f"loc_category:{site_id}:{category_id}"
            elif name:
                normalized_name = re.sub(r"\s+", "_", name.lower().strip())
                return f"loc_name:{site_id}:{normalized_name}"
            elif tags:
                sorted_tags = sorted(tags)
                tags_str = "_".join(sorted_tags)
                return f"loc_tags:{site_id}:{tags_str}"

        # Multi-parameter searches - use hash-based keys
        params = {
            "f": floor_id or "",  # Shorter keys
            "lt": location_type or "",
            "a": amenity_type or "",
            "c": category_id or "",
            "t": sorted(tags) if tags else [],
            "n": (name or "").lower().strip(),
        }

        # Remove empty parameters to improve cache hit rates
        params = {k: v for k, v in params.items() if v}

        # Create secure hash for complex queries
        params_hash = self._create_secure_params_hash(params)

        return f"loc_search:{site_id}:{params_hash}"

    def _get_cache_ttl(self, search_params: dict) -> int:
        """
        Determine appropriate cache TTL based on search parameters.

        Args:
            search_params: Dictionary of search parameters

        Returns:
            Cache TTL in seconds
        """
        # Popular/featured content changes more frequently
        if search_params.get("tags") and any(tag in ["popular", "featured"] for tag in search_params["tags"]):
            return 1800  # 30 minutes

        # Floor-based searches are stable
        if search_params.get("floor_id"):
            return 7200  # 2 hours

        # Amenity searches are very stable
        if search_params.get("amenity_type"):
            return 7200  # 2 hours

        # Default TTL
        return 3600  # 1 hour

    async def _execute_location_query(self, query: dict, floor_id: str | None = None) -> list[LocationInfo]:
        """
        Execute location query with automatic site_id filtering at MongoDB query level.
        Populates linked Node objects with complete NodeInfo data.

        Args:
            query: MongoDB query dictionary (without siteId - will be automatically added)
            floor_id: Floor ID parameter (used for logging/debugging purposes)

        Returns:
            List of LocationInfo objects with populated node Link fields
        """
        # Automatically bind site_id filter to the query for security
        current_site_id = RequestContextManager.get("site_id")
        query["siteId"] = current_site_id

        # Execute the query with proper sorting and fetch_links=True to populate Node references
        locations = await LocationInfo.find(query, fetch_links=True).sort("name").to_list()

        return locations


def get_locations_service() -> LocationsService:
    """Dependency to get locations service instance."""
    return LocationsService()


# Type alias for dependency injection
LocationsServiceDep = Annotated[LocationsService, Depends(get_locations_service)]
