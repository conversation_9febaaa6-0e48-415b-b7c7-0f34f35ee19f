"""Category information service."""

import json
import logging
from typing import Annotated

from beanie import PydanticObjectId
from fastapi import Depends

from src.context.request_context import RequestContextManager
from src.extensions.ext_redis import redis_client
from src.utils import is_valid_objectid

from ..exceptions import ResourceNotFoundError, ValidationError
from ..models.category import CategoryInfo

log = logging.getLogger(__name__)


class CategoriesService:
    """Service for category information operations."""

    async def get_category_by_id(self, category_id: str) -> CategoryInfo:
        """
        Fetch a category by its identifier using Beanie ODM.
        """
        if not category_id.strip() or not is_valid_objectid(category_id):
            raise ValidationError("Category ID must be provided")

        # TODO: Replace with authorization token from API header
        current_site_id = RequestContextManager.get("site_id")
        cache_key = f"category_id:{current_site_id}:{category_id}"
        cached_response = redis_client.get(cache_key)
        if cached_response:
            log.info(f"Returning cached category info for category_id: {category_id}")
            category_info = CategoryInfo.model_validate_json(cached_response.decode())

            # Cache already includes site_id scoping, no additional validation needed

            return category_info

        log.info(f"Fetching category info from database for category_id: {category_id}")
        category = await CategoryInfo.get(PydanticObjectId(category_id))
        if category is None:
            raise ResourceNotFoundError(f"Category with ID '{category_id}' not found")

        # Validate site_id scoping after database retrieval
        if category.site_id != current_site_id:
            raise ResourceNotFoundError(f"Category with ID '{category_id}' not found")

        redis_client.set(cache_key, category.to_json(), ex=3600)
        return category

    async def get_category_by_name(self, category_name: str) -> list[CategoryInfo]:
        """
        Search for a category by name within the current site using contains search.
        """
        if not category_name.strip():
            raise ValidationError("Category name must be provided")

        # TODO: Replace with authorization token from API header
        current_site_id = RequestContextManager.get("site_id")
        cache_key = f"category_search_array:{current_site_id}:{category_name.lower()}"
        cached_response = redis_client.get(cache_key)
        if cached_response:
            log.info(f"Returning cached category search results for category_name: {category_name}")
            categories_data = json.loads(cached_response.decode())
            categories = [CategoryInfo.model_validate(cat_data) for cat_data in categories_data]
            return categories

        log.info(f"Searching categories from database for category_name containing: {category_name}")
        # Use regex for case-insensitive contains search with site_id filtering in single query
        import re

        regex_pattern = re.compile(re.escape(category_name), re.IGNORECASE)
        categories = await CategoryInfo.find({"siteId": current_site_id, "name": {"$regex": regex_pattern}}).to_list()

        if not categories:
            raise ResourceNotFoundError(f"No categories found containing '{category_name}'")

        # Cache the list of categories as JSON array
        categories_json = json.dumps([json.loads(cat.to_json()) for cat in categories])
        redis_client.set(cache_key, categories_json, ex=3600)
        return categories


def get_categories_service() -> CategoriesService:
    """Dependency to get categories service instance."""
    return CategoriesService()


# Type alias for dependency injection
CategoriesServiceDep = Annotated[CategoriesService, Depends(get_categories_service)]
