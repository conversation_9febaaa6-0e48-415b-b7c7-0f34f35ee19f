"""Site information routes."""

from ...constants.mapping_constants import SITE_API
from ...mcp.descriptions import SITE_GET_INFO_DESCRIPTION
from ...models.sites import SiteInfo
from ...services.sites import SitesServiceDep
from ..base_router import BaseRouter
from ..core.cbv import cbv, get

sites_router = BaseRouter(
    prefix=SITE_API,
    tags=["sites"],
)


@cbv(sites_router)
class SiteController:
    @get(
        "/get-site-info",
        operation_id="get_site_info",
        response_model=SiteInfo,
        description=SITE_GET_INFO_DESCRIPTION,
    )
    async def get_site_info(
        self,
        site_id: str,
        sites_service: SitesServiceDep,
    ) -> SiteInfo:
        """Get comprehensive site information including floors by site ID."""
        return await sites_service.get_site_info(site_id)
