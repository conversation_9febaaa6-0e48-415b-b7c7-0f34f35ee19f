"""Location information routes."""

from typing import Annotated

from fastapi import Query

from ...constants.mapping_constants import LOCATION_API
from ...mcp.descriptions import LOCATION_GET_INFO_DESCRIPTION, LOCATION_SEARCH_DESCRIPTION
from ...models.location import LocationInfo
from ...services.locations import LocationsServiceDep
from ..base_router import BaseRouter
from ..core.cbv import cbv, get

locations_router = BaseRouter(
    prefix=LOCATION_API,
    tags=["locations"],
)


@cbv(locations_router)
class LocationController:
    @get(
        "/get-location-info",
        operation_id="get_location_info",
        response_model=LocationInfo,
        description=LOCATION_GET_INFO_DESCRIPTION,
    )
    async def get_location_info(
        self,
        location_id: str,
        locations_service: LocationsServiceDep,
    ) -> LocationInfo:
        """Get comprehensive location information by location ID."""
        return await locations_service.get_location_by_id(location_id)

    @get(
        "/search",
        operation_id="search_locations",
        response_model=list[LocationInfo],
        description=LOCATION_SEARCH_DESCRIPTION,
    )
    async def search_locations(
        self,
        locations_service: LocationsServiceDep,
        floor_id: Annotated[str | None, Query(description="Filter by specific floor ID (exact match)")] = None,
        location_type: Annotated[
            str | None, Query(description="Filter by location type (TENANT, AMENITIES, PARKING, etc.)")
        ] = None,
        amenity_type: Annotated[
            str | None, Query(description="Filter by amenity type (exact match, only for AMENITIES locations)")
        ] = None,
        category_id: Annotated[str | None, Query(description="Filter by category ID (exact match)")] = None,
        tags: Annotated[
            list[str] | None,
            Query(description="Filter by services offered and location highlights (array, matches any)"),
        ] = None,
        name: Annotated[
            str | None, Query(description="Filter by location name (case-insensitive contains/partial match)")
        ] = None,
    ) -> list[LocationInfo]:
        """
        Search for locations using flexible, combinable search criteria.
        At least one search parameter must be provided.
        """
        return await locations_service.search_locations(
            floor_id=floor_id,
            location_type=location_type,
            amenity_type=amenity_type,
            category_id=category_id,
            tags=tags,
            name=name,
        )
