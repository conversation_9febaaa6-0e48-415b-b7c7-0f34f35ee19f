"""Health check routes."""

from fastapi import HTTPException

from ...constants.mapping_constants import HEALTH_API
from ...models.health import HealthResponse
from ...services.health import HealthServiceDep
from ..base_router import BaseRouter
from ..core.cbv import cbv, get

health_router = BaseRouter(
    prefix=HEALTH_API,
    tags=["health"],
)


@cbv(health_router)
class HealthController:
    @get("/", response_model=HealthResponse, operation_id="get_health_status")
    async def health_check(
        self,
        health_service: HealthServiceDep,
    ) -> HealthResponse:
        """Get application health status."""
        try:
            return await health_service.get_health_status()
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Health check failed: {str(e)}",
            ) from e

    @get("/ready", response_model=HealthResponse)
    async def readiness_check(
        self,
        health_service: HealthServiceDep,
    ) -> HealthResponse:
        """Get application readiness status."""
        try:
            return await health_service.get_readiness_status()
        except Exception as e:
            raise HTTPException(
                status_code=503,
                detail=f"Readiness check failed: {str(e)}",
            ) from e

    @get("/live", response_model=HealthResponse)
    async def liveness_check(
        self,
        health_service: HealthServiceDep,
    ) -> HealthResponse:
        """Get application liveness status."""
        try:
            return await health_service.get_liveness_status()
        except Exception as e:
            raise HTTPException(
                status_code=503,
                detail=f"Liveness check failed: {str(e)}",
            ) from e
