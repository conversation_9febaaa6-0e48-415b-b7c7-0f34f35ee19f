"""Category information routes."""

from ...constants.mapping_constants import CA<PERSON>GORY_API
from ...mcp.descriptions import CATEGORY_GET_BY_NAME_DESCRIPTION, CATEGORY_GET_INFO_DESCRIPTION
from ...models.category import CategoryInfo
from ...services.categories import CategoriesServiceDep
from ..base_router import BaseRouter
from ..core.cbv import cbv, get

categories_router = BaseRouter(
    prefix=CATEGORY_API,
    tags=["categories"],
)


@cbv(categories_router)
class CategoryController:
    @get(
        "/get-category-info",
        operation_id="get_category_info",
        response_model=CategoryInfo,
        description=CATEGORY_GET_INFO_DESCRIPTION,
    )
    async def get_category_info(
        self,
        category_id: str,
        categories_service: CategoriesServiceDep,
    ) -> CategoryInfo:
        """Get comprehensive category information by category ID."""
        return await categories_service.get_category_by_id(category_id)

    @get(
        "/get-category-by-name",
        operation_id="get_category_by_name",
        response_model=list[CategoryInfo],
        description=CATEGORY_GET_BY_NAME_DESCRIPTION,
    )
    async def get_category_by_name(
        self,
        category_name: str,
        categories_service: CategoriesServiceDep,
    ) -> list[CategoryInfo]:
        """Search for category information using contains matching on category name."""
        return await categories_service.get_category_by_name(category_name)
