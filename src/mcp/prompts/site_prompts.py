"""Site-related MCP prompts for the Beco MCP Server."""

# Import the MCP wrapper
from ...extensions.ext_mcp import mcp_wrapper

mcp = mcp_wrapper


@mcp.prompt(
    name="site_information_assistant",
    description=(
        "Comprehensive venue information assistant. Use this for ANY query requiring venue details, "
        "floor information, operating hours, contact info, or general venue context. Essential for "
        "location-based assistance and navigation help."
    ),
    tags={"site-info", "venue", "location", "assistance", "floors", "building", "navigation", "hours", "contact"},
)
def site_information_assistant() -> str:
    """
    Enhanced prompt for comprehensive venue and floor information assistance.
    Use this whenever you need complete venue details including floor layouts.
    """
    return """
🏢 **VENUE INFORMATION ASSISTANT**

## 🎯 WHEN TO USE THIS PROMPT

**ALWAYS use get_site_info() for these query types:**

### 📍 **Location & Venue Queries**
- "Where am I?" / "What is this place?"
- "What type of venue is this?"
- "Tell me about this location"
- "What's the name of this building/mall/airport?"

### 🏗️ **Floor & Building Information**
- "How many floors are there?"
- "What floors does this venue have?"
- "Show me the floor layout"
- "Which floor am I on?"
- "What's on each floor?"
- "Floor directory" / "Building map"

### 🕒 **Operating Hours & Schedule**
- "What time does this place open/close?"
- "What are the operating hours?"
- "Is this place open now?"
- "When does [venue] close today?"

### 📞 **Contact & Website Information**
- "How can I contact this venue?"
- "What's the phone number?"
- "Do you have a website?"
- "Customer service contact"

### 🗺️ **Address & Location Details**
- "What's the address of this place?"
- "Where exactly am I located?"
- "GPS coordinates" / "Location details"
- "How do I get here?" (for address context)

## 🛠️ TOOL USAGE: get_site_info()

**This tool provides COMPLETE venue information including:**

### 🏢 **Site Details**
- Site ID, name, and official designation
- Venue type (MALL, AIRPORT, UNIVERSITY, OFFICE, HOSPITAL, etc.)
- Geographic coordinates (latitude, longitude)
- Complete postal address with city, state, country
- Timezone information and UTC offset

### 🏗️ **COMPREHENSIVE FLOOR INFORMATION**
- **Complete floor directory** with all floors in the venue
- **Floor IDs** (ObjectId) for each floor - essential for location searches
- **Floor names** (e.g., "Ground Floor", "Level 2", "Basement")
- **Short names** (e.g., "GF", "L2", "B1") for quick reference
- **Elevation levels** and building associations
- **Floor hierarchy** and layout structure

### 📅 **Operational Information**
- Daily operating hours for each day of the week
- Special hours or holiday schedules
- Contact telephone and website links
- Top/featured locations within the venue

## 📋 EXECUTION STEPS

### **STEP 1: ALWAYS CALL get_site_info() FIRST**
```
Call: get_site_info()
```

### **STEP 2: ANALYZE THE RESPONSE**
Extract and organize:
- Venue identification and type
- Complete floor directory with IDs and names
- Operating schedule and contact details
- Address and location context

### **STEP 3: PRESENT INFORMATION CLEARLY**

**Use this structured format:**

```
🏢 **VENUE INFORMATION**

📍 **Location:** [Site Name]
🏗️ **Type:** [Venue Type - MALL/AIRPORT/etc.]
📍 **Address:** [Complete Address]

🏗️ **FLOOR DIRECTORY:**
[List all floors with names and short names]
• [Floor Name] ([Short Name]) - Floor ID: [ObjectId]
• [Continue for all floors...]

🕒 **OPERATING HOURS:**
[Daily schedule with days and times]

📞 **CONTACT INFORMATION:**
• Phone: [Phone Number]
• Website: [Website URL]

🌍 **LOCATION DETAILS:**
• Coordinates: [Lat, Lng]
• Timezone: [Timezone] (UTC[Offset])
```

## 🎯 COMMON USE CASES

### **Floor Information Requests**
- **Query:** "What floors are available?"
- **Action:** Call get_site_info() → Present complete floor directory
- **Include:** Floor names, short names, and IDs for future searches

### **Venue Context for Navigation**
- **Query:** "Help me navigate this place"
- **Action:** Call get_site_info() → Provide venue overview and floor structure
- **Include:** Venue type context and floor layout

### **Operating Hours Inquiries**
- **Query:** "When does this place close?"
- **Action:** Call get_site_info() → Extract and present current day's hours
- **Include:** Full weekly schedule for reference

### **General Venue Information**
- **Query:** "Tell me about this venue"
- **Action:** Call get_site_info() → Comprehensive venue overview
- **Include:** All available details formatted clearly

## ⚠️ IMPORTANT NOTES

1. **ALWAYS call get_site_info() first** - Never assume venue details
2. **Floor IDs are crucial** - Include ObjectIds for floor-based searches
3. **Present complete floor directory** - Users need to know all available floors
4. **Include venue type context** - Helps users understand the space
5. **Format clearly** - Use emojis and structure for readability

## 🔄 FOLLOW-UP ACTIONS

After providing site information, be ready to:
- Help with specific floor-based location searches
- Assist with amenity finding on specific floors
- Provide navigation guidance using floor context
- Answer detailed questions about venue facilities

**Remember: get_site_info() is your primary tool for ANY venue-related query. Use it liberally to provide accurate,
current information.**
"""


@mcp.prompt(
    name="floor_directory_assistant",
    description=(
        "Specialized assistant for floor-related queries. Use this when users ask about floors, "
        "building levels, floor directories, or need floor information for navigation."
    ),
    tags={"floors", "building", "levels", "directory", "navigation", "venue-structure"},
)
def floor_directory_assistant(query: str) -> str:
    """
    Specialized prompt for floor-related queries that directs to site_information_assistant.
    Args:
        query: User query about floors or building levels
    """
    return f"""
🏗️ **FLOOR DIRECTORY REQUEST**: "{query}"

## IMMEDIATE ACTION REQUIRED:
**Use the site_information_assistant prompt** to handle this floor-related query.

## WHY USE site_information_assistant:
Your query is about floors, building levels, or venue structure. The site_information_assistant prompt is
specifically designed to:

✅ **Get complete floor directory** via get_site_info()
✅ **Provide floor IDs** needed for location searches
✅ **Show floor names and short names** for navigation
✅ **Present building structure** and layout information
✅ **Include elevation levels** and floor hierarchy

## FLOOR-RELATED QUERIES HANDLED:
- "What floors are there?" / "How many floors?"
- "Floor directory" / "Building map"
- "Which floor am I on?" / "Floor information"
- "What's on each floor?" / "Floor layout"
- "Ground floor" / "Level 2" / "Basement" references

## REQUIRED ACTION:
**Invoke the site_information_assistant prompt now** to get comprehensive venue and floor information using
get_site_info().

The site_information_assistant will provide the complete floor directory with IDs, names, and structure details
needed to answer your floor-related question.
"""


@mcp.prompt(
    name="venue_contact_assistant",
    description="Generates a prompt for handling venue contact and information requests",
    tags={"contact", "venue", "information", "assistance"},
)
def venue_contact_assistant(inquiry_type: str = "general") -> str:
    """
    Creates a prompt for handling various types of venue contact and information requests.
    Args:
        inquiry_type: Type of inquiry (general, hours, contact, location, etc.)
    """
    inquiry_prompts = {
        "general": "general venue information",
        "hours": "operating hours and schedule",
        "contact": "contact details and communication",
        "location": "location and address information",
        "facilities": "venue facilities and services",
    }
    inquiry = inquiry_prompts.get(inquiry_type, "venue information")
    return (
        f"The user is requesting {inquiry}.\n\n"
        "I need to provide accurate and up-to-date information about the current venue. "
        "Let me use the get_site_info tool to retrieve the latest venue details.\n\n"
        "Based on the venue information, I will provide:\n"
        "- Relevant details specific to their inquiry\n"
        "- Contact information if needed\n"
        "- Operating hours if relevant\n"
        "- Location details if applicable\n"
        "- Any additional context that might be helpful\n\n"
        "Please use the get_site_info tool to get current venue information before responding."
    )


@mcp.prompt(
    name="location_search_assistant",
    description=(
        "Universal location finder for business and service discovery. Use this for ANY query about "
        "finding places, businesses, restaurants, shops, or services. Handles 'where can I get/find' "
        "questions, quality-based searches (best, cheap, popular), and service-based discovery. "
        "Specializes in tags-based searches and category combinations."
    ),
    tags={
        "location-search",
        "where-can-i-get",
        "where-can-i-find",
        "where-to-buy",
        "where-can-i-buy",
        "business-search",
        "restaurant-search",
        "shop-search",
        "service-discovery",
        "tags-search",
        "best",
        "cheap",
        "popular",
        "quality",
        "food",
        "furniture",
        "shopping",
        "dining",
        "services",
        "highlights",
        "categories",
        "electronics",
        "phone",
        "iphone",
        "mobile",
        "technology",
        "gadgets",
        "buy",
        "purchase",
    },
)
def location_search_assistant(search_intent: str) -> str:
    """
    Creates a prompt for structured location searches using the search_locations tool.
    Args:
        search_intent: The type of search intent (amenity, category, service, floor, etc.)
    """
    return f"""
🔍 **UNIVERSAL LOCATION FINDER**

**Search Intent**: "{search_intent}"

## 🎯 QUERY TYPES HANDLED

### **"Where can I get/find..." Queries**:
- "Where can I get best fries chicken?" → Food/restaurant search with quality tags
- "Where can I get cheap economic furniture?" → Furniture/retail search with price tags
- "Where can I find good coffee?" → Coffee shop search with quality tags
- "Where can I get fast food?" → Fast food category with speed tags

### **"Where to buy..." Product Queries**:
- "Where to buy an iPhone?" → Electronics/mobile shop search with product tags
- "Where can I buy electronics?" → Electronics category with product tags
- "Where to purchase gadgets?" → Technology/electronics search with product tags
- "Where can I buy a phone?" → Mobile/electronics shop search with device tags

### **Quality & Price-Based Queries**:
- "Best restaurants" → Restaurant category + "best", "popular", "top_rated" tags
- "Cheap furniture" → Furniture category + "cheap", "affordable", "budget" tags
- "Popular shops" → Retail category + "popular", "featured", "trending" tags
- "High-quality services" → Service category + "premium", "quality", "professional" tags

## 🛠️ SEARCH_LOCATIONS TOOL PARAMETERS

### **Core Parameters**:
- **floor_id**: Filter by specific floor (use ObjectId from site info)
- **location_type**: Filter by location type (TENANT, AMENITIES, PARKING, SEATING, GATE, etc.)
- **amenity_type**: Filter by amenity type (restroom, atm, elevator, prayer-room, etc.) - only for AMENITIES locations
- **category_id**: Filter by category (use ObjectId from category search)
- **name**: Filter by location name (case-insensitive partial matching)

### **🏷️ TAGS PARAMETER - Services & Highlights Search**:
**The tags parameter is POWERFUL for finding locations by services offered and highlights:**
- **Type**: Array of strings `tags=["tag1", "tag2", "tag3"]`
- **Matching**: Returns locations that offer ANY of the specified services/highlights
- **Case-sensitive**: Use exact tag strings as stored in location data
- **Contains search**: Searches within the location's tags array

## 🏷️ COMMON SERVICES & HIGHLIGHTS TAGS

### **🌐 Connectivity & Technology**:
- `"wifi"` - Free WiFi available
- `"charging_station"` - Device charging stations
- `"free_wifi"` - Complimentary internet access
- `"high_speed_internet"` - Fast internet connection

### **♿ Accessibility & Comfort**:
- `"wheelchair_accessible"` - Wheelchair accessible
- `"accessible"` - General accessibility features
- `"elevator_access"` - Accessible via elevator
- `"ramp_access"` - Wheelchair ramp available
- `"accessible_parking"` - Accessible parking spots

### **🕒 Operating Hours & Availability**:
- `"24_hours"` - Open 24 hours
- `"24/7"` - Always open
- `"extended_hours"` - Late operating hours
- `"early_opening"` - Opens early
- `"weekend_hours"` - Weekend availability

### **👨‍👩‍👧‍👦 Family & Social**:
- `"family_friendly"` - Suitable for families
- `"kids_play_area"` - Children's play area
- `"baby_changing"` - Baby changing facilities
- `"stroller_friendly"` - Stroller accessible
- `"quiet_zone"` - Quiet environment

### **🍽️ Food & Dining Services**:
- `"takeaway"` - Takeout available
- `"delivery"` - Delivery service
- `"drive_through"` - Drive-through service
- `"outdoor_seating"` - Outdoor dining area
- `"halal"` - Halal food options
- `"vegetarian"` - Vegetarian options
- `"vegan"` - Vegan options

### **🏆 Quality & Popularity**:
- `"popular"` - Highly rated/popular
- `"best"` - Best in category
- `"top_rated"` - Top-rated location
- `"featured"` - Featured location
- `"recommended"` - Recommended by venue
- `"award_winning"` - Award-winning establishment
- `"premium"` - Premium quality/service
- `"quality"` - High quality
- `"excellent"` - Excellent rating

### **💰 Price & Value**:
- `"cheap"` - Low cost/budget-friendly
- `"affordable"` - Reasonably priced
- `"budget"` - Budget options
- `"economic"` - Economical pricing
- `"value"` - Good value for money
- `"discount"` - Discounted prices
- `"sale"` - On sale/promotional pricing

### **💳 Payment & Services**:
- `"card_payment"` - Accepts card payments
- `"contactless_payment"` - Contactless payment
- `"cash_only"` - Cash payments only
- `"loyalty_program"` - Loyalty rewards program

### **📱 Electronics & Technology**:
- `"electronics"` - Electronics store/section
- `"mobile"` - Mobile phones and accessories
- `"phone"` - Phone sales and services
- `"iphone"` - iPhone sales and support
- `"android"` - Android devices
- `"smartphone"` - Smartphone sales
- `"gadgets"` - Technology gadgets
- `"technology"` - Technology products
- `"computers"` - Computer sales and services
- `"accessories"` - Tech accessories
- `"repair"` - Device repair services

## 🔍 SEARCH STRATEGIES BY INTENT

### **🎯 "Where can I get/find..." Queries** (PRIMARY USE CASE):
**For queries like**: "Where can I get best fries chicken?", "Where can I find cheap furniture?"

**Strategy**:
1. **Extract item/service**: Identify what user is looking for (food, furniture, etc.)
2. **Extract quality/price modifiers**: Identify descriptors (best, cheap, popular, etc.)
3. **Find category**: Use `get_category_by_name()` to find relevant category
4. **Apply tags**: Use quality/price tags for filtering
5. **Combine search**: `search_locations(category_id="category_id", tags=["quality_tags"])`

**Examples**:
- **"Where can I get best fries chicken?"**:
  - Category: "restaurant" or "fast_food"
  - Tags: `["best", "popular", "fried_chicken"]`
  - Call: `search_locations(category_id="restaurant_id", tags=["best", "popular", "fried_chicken"])`

- **"Where can I get cheap economic furniture?"**:
  - Category: "furniture" or "home_goods"
  - Tags: `["cheap", "affordable", "budget", "economic"]`
  - Call: `search_locations(category_id="furniture_id", tags=["cheap", "affordable", "budget"])`

- **"Where to buy an iPhone?"**:
  - Category: "electronics" or "mobile" or "technology"
  - Tags: `["iphone", "mobile", "phone", "electronics"]`
  - Call: `search_locations(category_id="electronics_id", tags=["iphone", "mobile", "phone"])`
  - Alternative: `search_locations(name="electronics", tags=["iphone", "mobile", "phone"])`

### **🏷️ Service-Based Search Intent**:
**For queries like**: "Places with WiFi", "Wheelchair accessible locations", "24-hour services"

**Strategy**:
- **Primary tool**: `tags` parameter with service keywords
- **Examples**:
  - `search_locations(tags=["wifi"])` - All WiFi locations
  - `search_locations(tags=["wheelchair_accessible"])` - Accessible locations
  - `search_locations(tags=["24_hours"])` - 24-hour services
  - `search_locations(tags=["family_friendly", "kids_play_area"])` - Family locations
  - `search_locations(tags=["popular", "featured"])` - Popular/featured locations

### **🚻 Amenity Search Intent**:
- Use `location_type="AMENITIES"` + `amenity_type` for specific amenities
- Combine with `tags` for service-enhanced amenity searches
- **Examples**:
  - `search_locations(amenity_type="restroom", tags=["wheelchair_accessible"])`
  - `search_locations(location_type="AMENITIES", floor_id="floor_id")`

### **🏪 Category + Service Search Intent**:
- Combine `category_id` with `tags` for enhanced category searches
- **Examples**:
  - `search_locations(category_id="restaurant_id", tags=["popular", "outdoor_seating"])`
  - `search_locations(category_id="retail_id", tags=["wheelchair_accessible"])`

### **🏗️ Floor + Service Search Intent**:
- Combine `floor_id` with `tags` for floor-specific service searches
- **Examples**:
  - `search_locations(floor_id="floor_id", tags=["wifi", "quiet_zone"])`
  - `search_locations(floor_id="ground_floor_id", tags=["wheelchair_accessible"])`

### **🔤 Name + Service Search Intent**:
- Combine `name` with `tags` for enhanced name searches
- **Examples**:
  - `search_locations(name="coffee", tags=["wifi", "outdoor_seating"])`
  - `search_locations(name="restaurant", tags=["halal", "family_friendly"])`

## 📋 RESPONSE GUIDELINES

### **🔍 Search Execution**:
1. **Parameter Validation**: Ensure at least one search parameter is provided
2. **ObjectId Usage**: Use proper ObjectIds for `floor_id` and `category_id` parameters
3. **Tags Array Format**: Use proper array format: `tags=["tag1", "tag2"]`
4. **Case Sensitivity**: Use exact tag strings as they appear in location data

### **📊 Result Presentation**:
```
🏪 **[Service/Highlight] Locations Found:**

📍 **[Location Name]**
   - 🏢 Floor: [Floor Name]
   - 🏷️ Services: [List of available services from tags]
   - ♿ Accessibility: [Accessibility features]
   - 🕒 Hours: [Operating hours if available]
   - 📍 Description: [Location details]

[Repeat for each location]
```

### **🔄 Fallback Strategies**:
- **No Results**: Try broader tags or remove some filters
- **Too Many Results**: Add more specific tags or floor filtering
- **Related Searches**: Suggest similar services or nearby alternatives

## 🎯 PRACTICAL USAGE EXAMPLES

### **"Where can I get..." Business Discovery**:
- **"Where can I get best fries chicken?"** →
  `search_locations(category_id="restaurant_id", tags=["best", "popular", "fried_chicken"])`
- **"Where can I get cheap furniture?"** →
  `search_locations(category_id="furniture_id", tags=["cheap", "affordable", "budget"])`
- **"Where can I find good coffee?"** → `search_locations(name="coffee", tags=["best", "popular", "quality"])`
- **"Where can I get fast food?"** → `search_locations(category_id="fast_food_id", tags=["quick", "fast"])`

### **Product Purchase Queries**:
- **"Where to buy an iPhone?"** → `search_locations(category_id="electronics_id", tags=["iphone", "mobile", "phone"])`
- **"Where can I buy electronics?"** → `search_locations(category_id="electronics_id", tags=["electronics", "gadgets"])`
- **"Where to purchase gadgets?"** → `search_locations(name="electronics", tags=["gadgets", "technology"])`
- **"Where can I buy a phone?"** → `search_locations(tags=["mobile", "phone", "smartphone"])`

### **Quality-Based Business Queries**:
- **"Best restaurants"** → `search_locations(category_id="restaurant_id", tags=["best", "top_rated", "popular"])`
- **"Cheap shops"** → `search_locations(category_id="retail_id", tags=["cheap", "affordable", "budget"])`
- **"Popular places"** → `search_locations(tags=["popular", "featured", "trending"])`
- **"Premium services"** → `search_locations(tags=["premium", "quality", "professional"])`

### **Service Discovery Queries**:
- **"Places with WiFi"** → `search_locations(tags=["wifi"])`
- **"Wheelchair accessible restaurants"** →
  `search_locations(category_id="restaurant_id", tags=["wheelchair_accessible"])`
- **"24-hour services"** → `search_locations(tags=["24_hours", "24/7"])`
- **"Family-friendly places"** → `search_locations(tags=["family_friendly", "kids_play_area"])`

### **Highlight-Based Queries**:
- **"Popular restaurants"** → `search_locations(category_id="restaurant_id", tags=["popular", "featured"])`
- **"Quiet places to work"** → `search_locations(tags=["wifi", "quiet_zone"])`
- **"Outdoor dining"** → `search_locations(tags=["outdoor_seating", "takeaway"])`

### **Complex Service Combinations**:
- **"Accessible coffee shops with WiFi"** → `search_locations(name="coffee", tags=["wheelchair_accessible", "wifi"])`
- **"Popular places on ground floor"** → `search_locations(floor_id="ground_floor_id", tags=["popular", "featured"])`

**CRITICAL VALIDATION**: Always provide at least one search parameter when calling search_locations().
Never call search_locations() without any parameters - this will cause a validation error.

**IMPORTANT**: The `tags` parameter is your primary tool for finding locations by services offered and highlights.
Use it extensively for service-based location discovery.
"""


@mcp.prompt(
    name="location_finder_by_category_and_floor",
    description=(
        "Universal location finder for category-based searches with optional floor filtering. "
        "Use this for queries like 'restaurants on ground floor', 'shops on level 2', "
        "'coffee shops', 'clothing stores', or any business/category searches."
    ),
    tags={
        "category-search",
        "location-search",
        "venue-navigation",
        "restaurants",
        "shops",
        "stores",
        "coffee",
        "food",
        "retail",
        "business",
        "tenant",
        "floor-search",
    },
)
def location_finder_by_category_and_floor(category_name: str, floor_name: str | None = None) -> str:
    """
    Enhanced instructions for finding locations by category with optional floor filtering.
    Args:
        category_name: The category user is searching for (e.g., 'restaurant', 'coffee', 'shop', 'clothing')
        floor_name: The floor user specified (e.g., 'ground floor', 'level 2', may be None if not specified)
    """
    return f"""
🔍 **CATEGORY-BASED LOCATION SEARCH**: "{category_name}" {f'on "{floor_name}"' if floor_name else ""}

## STEP 1: FIND CATEGORY ID

**Category Search**: "{category_name}"
- **Primary Action**: Call `get_category_by_name("{category_name}")` to find matching categories
- **Alternative**: If no exact match, try partial matches or synonyms:
  - "restaurant" → "dining", "food", "eatery"
  - "shop" → "retail", "store", "shopping"
  - "coffee" → "café", "coffee shop", "beverage"
  - "clothing" → "apparel", "fashion", "garments"

## STEP 2: HANDLE FLOOR FILTERING (if specified)

**Floor Reference**: {f'"{floor_name}"' if floor_name else "None specified"}
{
        f'''
- **Get Site Info**: Call `get_site_info()` to retrieve complete floor directory
- **Match Floor**: Find floor with name or shortName matching "{floor_name}"
- **Extract Floor ID**: Get the ObjectId for the matched floor
- **Floor Keywords**: "ground floor", "level 1", "L2", "basement", etc.
'''
        if floor_name
        else "- **No floor filtering**: Search all floors in venue"
    }

## STEP 3: EXECUTE LOCATION SEARCH

**Search Parameters**:
- `category_id`: ObjectId from Step 1
{
        "- `floor_id`: ObjectId from Step 2 (floor filtering)"
        if floor_name
        else "- `floor_id`: Not specified (search all floors)"
    }
- `location_type`: "TENANT" (for business/retail locations)

**Search Call**:
```
search_locations(
    category_id="[category_objectid]",
    {'floor_id="[floor_objectid]",' if floor_name else "# floor_id not specified"}
    location_type="TENANT"
)
```

## STEP 4: PRESENT RESULTS

**Format Results Like This**:
```
🏪 **{category_name.title()} Locations Found:**

📍 **[Location Name]**
   - 🏢 Floor: [Floor Name]
   - 📍 Category: [Category Name]
   - 🏷️ Tags: [Available services/features]
   - 📍 Description: [Location details]

[Repeat for each location]
```

## STEP 5: HANDLE NO RESULTS

**If No Locations Found**:
1. **Suggest Similar Categories**: Try related category names
2. **Expand Floor Search**: If floor specified, try other floors
3. **Broaden Search**: Remove floor filter and search all floors
4. **Alternative Suggestions**: Recommend similar business types

**Example Fallbacks**:
- "restaurant" → try "food", "dining", "café"
- "shop" → try "retail", "store", "boutique"
- Specific floor → try "all floors" or "nearby floors"

## COMMON CATEGORY MAPPINGS

**Food & Dining**:
- restaurant, dining, food, eatery → Food/Restaurant categories
- coffee, café, beverage → Coffee/Café categories
- fast food, quick service → Fast Food categories

**Retail & Shopping**:
- shop, store, retail → Shopping/Retail categories
- clothing, apparel, fashion → Clothing/Fashion categories
- electronics, tech → Electronics categories

**Services**:
- bank, banking → Financial Services
- pharmacy, medical → Healthcare/Pharmacy
- beauty, salon → Beauty/Personal Care

**IMPORTANT**: Always use `location_type="TENANT"` for business/retail location searches.
"""


@mcp.prompt(
    name="find_amenity",
    description=(
        "Universal amenity finder for ALL facility searches. Use this for ANY query about restrooms, "
        "washrooms, toilets, ATMs, elevators, information desks, prayer rooms, or any venue facilities. "
        "Handles floor-specific searches, synonyms, and all amenity types."
    ),
    tags={
        "amenity-search",
        "facilities",
        "restroom",
        "washroom",
        "toilet",
        "bathroom",
        "atm",
        "elevator",
        "lift",
        "information",
        "prayer-room",
        "first-aid",
        "drinking-water",
        "vending-machine",
        "location-search",
        "venue-navigation",
    },
)
def find_amenity(query: str) -> str:
    """
    Enhanced prompt for finding amenities with better synonym handling and floor extraction.
    Args:
        query: User input for amenity search (e.g. "where is the washroom in ground floor", "restroom on level 2").
    """

    return f"""
🔍 **AMENITY SEARCH QUERY**: "{query}"

## STEP 1: IDENTIFY AMENITY TYPE

**Primary Amenity Keywords** (map user terms to these exact amenity_type values):
- **Restroom/Washroom/Toilet/Bathroom** → "restroom"
- **Men's Restroom/Washroom** → "washroom-men"
- **Women's Restroom/Washroom** → "washroom-women"
- **Family Restroom/Washroom** → "washroom-family"
- **Accessible Restroom/Washroom** → "washroom-accessible"
- **ATM/Cash Machine** → "atm"
- **Elevator/Lift** → "elevator"
- **Prayer Room/Prayer Hall** → "prayer-room"
- **Information Desk/Info Counter** → "information-desk"
- **Food Court/Food Hall** → "food-court"
- **First Aid/Medical** → "first-aid"
- **Drinking Water/Water Fountain** → "drinking-water"
- **Vending Machine** → "vending-machine"

**Additional Amenities**: pickup-point, feeding-room, taxi-stand, prepaid-taxi, bus-terminal, metro-exit,
check-in-counter, baggage-belt, baggage-counter, locker, currency-exchange, wallet-parking, reception,
help-desk, billing-counter, pram

## STEP 2: EXTRACT FLOOR INFORMATION

**Floor Keywords to Look For**:
- "ground floor", "ground level", "level 0", "floor 0" → Ground floor
- "first floor", "level 1", "floor 1", "1st floor" → First floor
- "second floor", "level 2", "floor 2", "2nd floor" → Second floor
- "basement", "lower level", "B1", "level -1" → Basement level
- "upper level", "top floor", "highest floor" → Upper floors

## STEP 3: EXECUTE AMENITY SEARCH

**Required Actions**:
1. **Get Site Information**: Call `get_site_info()` to retrieve venue details and floor list
2. **Match Floor**: If floor mentioned in query, find matching floor from site info by name/shortName
3. **Search Amenities**: Call `search_locations()` with these parameters:
   - `location_type="AMENITIES"` (REQUIRED - never call without parameters)
   - `amenity_type="[matched_amenity]"` (from Step 1)
   - `floor_id="[floor_objectid]"` (if floor specified and found)

**CRITICAL**: Always provide at least `location_type="AMENITIES"` - never call search_locations() without any
parameters.

**Example Search Calls**:
- For "washroom in ground floor":
  `search_locations(location_type="AMENITIES", amenity_type="restroom", floor_id="ground_floor_id")`
- For "ATM on level 2": `search_locations(location_type="AMENITIES", amenity_type="atm", floor_id="level2_floor_id")`
- For "elevator": `search_locations(location_type="AMENITIES", amenity_type="elevator")`

## STEP 4: HANDLE RESULTS

**If Amenities Found**:
- Present each location with: name, floor, accessibility info, operating status
- Include directions or landmarks if available
- Mention if multiple options exist on different floors

**If No Amenities Found**:
- Try broader search: `search_locations(location_type="AMENITIES")` to see all available amenities
- Suggest similar amenities if exact match not found
- As last resort, try category search for the main subject (e.g., "restroom" → search for "facilities" category)

## STEP 5: RESPONSE FORMAT

**Present Results Like This**:
```
🚻 **[Amenity Type] Locations Found:**

📍 **[Location Name]**
   - 🏢 Floor: [Floor Name]
   - ♿ Accessibility: [Yes/No/Details]
   - 🕒 Status: [Open/Closed/Hours]
   - 📍 Location: [Additional details if available]

[Repeat for each location found]
```

**IMPORTANT**: Always use `location_type="AMENITIES"` when searching for amenities. This is crucial for proper
filtering.
"""
