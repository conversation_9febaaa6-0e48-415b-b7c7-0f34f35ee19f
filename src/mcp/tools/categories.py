"""Category-related MCP tools."""

from src.extensions.ext_mcp import mcp_wrapper
from src.models.category import CategoryInfo
from src.services.categories import CategoriesService

from ..descriptions import CATEGORY_GET_BY_NAME_DESCRIPTION, CATEGORY_GET_INFO_DESCRIPTION

mcp = mcp_wrapper


@mcp.tool(
    name="get_category_info",  # Custom tool name for the LLM
    description=CATEGORY_GET_INFO_DESCRIPTION,  # Custom description
    tags={"get-category", "category-details", "classification"},  # Optional tags for organization/filtering
)
async def get_category_info(category_id: str) -> CategoryInfo:
    """
    Get comprehensive information about a specific category by ID.
    This tool retrieves detailed information about a category within the current site.
    """
    categories_service = CategoriesService()
    return await categories_service.get_category_by_id(category_id)


@mcp.tool(
    name="get_category_by_name",  # Custom tool name for the LLM
    description=CATEGORY_GET_BY_NAME_DESCRIPTION,  # Custom description
    tags={"get-category", "category-search", "classification"},  # Optional tags for organization/filtering
)
async def get_category_by_name(category_name: str) -> list[CategoryInfo]:
    """
    Search for a category using contains matching on the category name.
    This tool performs a case-insensitive search to find categories whose names contain the search term.
    """
    categories_service = CategoriesService()
    return await categories_service.get_category_by_name(category_name)
