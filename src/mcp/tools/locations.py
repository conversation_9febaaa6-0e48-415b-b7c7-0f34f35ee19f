"""Location-related MCP tools."""

from src.extensions.ext_mcp import mcp_wrapper
from src.models.location import LocationInfo
from src.services.locations import LocationsService

from ..descriptions import LOCATION_GET_INFO_DESCRIPTION, LOCATION_SEARCH_DESCRIPTION

mcp = mcp_wrapper


@mcp.tool(
    name="get_location_info",  # Custom tool name for the LLM
    description=LOCATION_GET_INFO_DESCRIPTION,  # Custom description
    tags={"get-location", "location-details", "venue-info"},  # Optional tags for organization/filtering
)
async def get_location_info(location_id: str) -> LocationInfo:
    """
    Get comprehensive information about a specific location.
    This tool retrieves detailed information about a location within the current site.
    """
    locations_service = LocationsService()
    return await locations_service.get_location_by_id(location_id)


@mcp.tool(
    name="search_locations",  # Custom tool name for the LLM
    description=LOCATION_SEARCH_DESCRIPTION,  # Custom description
    tags={
        "search-locations",
        "location-search",
        "venue-search",
        "find-locations",
    },  # Optional tags for organization/filtering
)
async def search_locations(
    floor_id: str | None = None,
    location_type: str | None = None,
    amenity_type: str | None = None,
    category_id: str | None = None,
    tags: list[str] | None = None,  # Services offered and location highlights
    name: str | None = None,
) -> list[LocationInfo]:
    """
    Search for locations using flexible, combinable search criteria.
    At least one search parameter must be provided to perform the search.

    This tool allows you to find locations within the current site using multiple
    optional search parameters that can be combined for precise results.
    """
    locations_service = LocationsService()
    return await locations_service.search_locations(
        floor_id=floor_id,
        location_type=location_type,
        amenity_type=amenity_type,
        category_id=category_id,
        tags=tags,
        name=name,
    )
