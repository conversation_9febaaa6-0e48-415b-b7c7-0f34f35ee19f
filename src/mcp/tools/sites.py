"""Site-related MCP tools."""

from src.context.request_context import Request<PERSON>ontextManager
from src.extensions.ext_mcp import mcp_wrapper
from src.models.sites import SiteInfo
from src.services.sites import SitesService

from ..descriptions import SITE_GET_INFO_DESCRIPTION

mcp = mcp_wrapper


@mcp.tool(
    name="get_site_info",  # Custom tool name for the LLM
    description=SITE_GET_INFO_DESCRIPTION,  # Custom description
    tags={"get-site", "venue-details"},  # Optional tags for organization/filtering
)
async def get_site_info() -> SiteInfo:
    """
    Get comprehensive information about a site.
    This tool retrieves detailed information about a specific site
    """
    sites_service = SitesService()
    site_id = RequestContextManager.get("site_id")
    return await sites_service.get_site_info(site_id)
