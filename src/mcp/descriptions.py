"""API endpoint descriptions for the Beco MCP Server."""

SITE_GET_INFO_DESCRIPTION = """
Retrieve comprehensive information about a Site (Venue) in the Beco ecosystem, including floor details.

A 'Site' represents a major venue such as a mall, airport, university, office, or similar large indoor location.
This endpoint provides a detailed overview of the site, including:

- **Site ID:** Unique identifier for the site
- **Site Name:** Official name of the venue
- **Operation Hours:** List of opening and closing times for each day of the week
- **Geographic Coordinates:** Latitude and longitude for mapping and navigation
- **Address:** Full postal address of the site
- **City, State, Country Code, Postal Code:** Location details for the venue
- **Contact Telephone:** Main contact number for the site
- **Top Locations:** List of highlighted or popular location IDs within the site
- **Time Zone (tzId):** Time zone identifier for the site
- **UTC Offset:** Time zone offset from UTC
- **Website Link:** Official website URL for the site
- **Type:** The type/category of the site (e.g., MALL, AIRPORT, UNIVERSITY, OFFICE)
- **Floors:** Array of floor information including floor ID, name, short name, elevation, and building association

Each floor object contains:
- **Floor ID:** Unique identifier for the floor
- **Name:** Full name of the floor
- **Short Name:** Abbreviated floor name
- **Elevation:** Floor elevation level
- **Building ID:** Associated building identifier
- **Site ID:** Associated site identifier (for reference)

Returns a structured `SiteInfo` object containing all available site metadata and floor details,
suitable for use in navigation, display, spatial queries, or further location-based operations.
"""

LOCATION_GET_INFO_DESCRIPTION = """
Retrieve comprehensive information about a specific Location within the current site.

A 'Location' represents a specific point of interest within a site, such as a store, amenity,
service location, or other notable place. This endpoint provides detailed information including:

- **Location ID:** Unique identifier for the location
- **Site ID:** Associated site identifier (automatically scoped to current site)
- **Name:** Official name of the location
- **Description:** Detailed description of the location
- **Category:** Location category classification
- **Tags:** Array of descriptive tags representing service offered or highlights for the location .
- **Amenity Type:** Type of amenity (for amenity locations)
- **Type:** Location type (TENANT, AMENITIES, PARKING, SEATING, GATE, etc.)

Returns a structured `LocationInfo` object containing all available location metadata,
suitable for use in navigation, wayfinding, search, and location-based services.

The location must belong to the current site context for security and data isolation.
"""

CATEGORY_GET_INFO_DESCRIPTION = """
Retrieve comprehensive information about a specific Category by its ID within the current site.

A 'Category' represents a classification system for locations and events within a site,
such as "Restaurant", "Shop Type", "Amenity", or "Service". This endpoint provides detailed
information including:

- **Category ID:** Unique identifier for the category
- **Site ID:** Associated site identifier (automatically scoped to current site)
- **Name:** Official name of the category

Returns a structured `CategoryInfo` object containing all available category metadata,
suitable for use in location classification, filtering, search, and organizational purposes.

The category must belong to the current site context for security and data isolation.
"""

CATEGORY_GET_BY_NAME_DESCRIPTION = """
Search for a Category by name within the current site using contains matching.

This endpoint performs a case-insensitive search to find categories whose names contain
the provided search term, which is useful for:

- **Fuzzy searches:** Finding categories with partial name matches
- **User-friendly queries:** Using partial category names in natural language interfaces
- **Content discovery:** Discovering categories without knowing exact names
- **Search functionality:** Implementing category search features

Examples:
- Searching for "rest" might find "Restaurant", "Rest Area", or "Restroom"
- Searching for "shop" might find "Shopping", "Gift Shop", or "Coffee Shop"

Returns an array of matching `CategoryInfo` objects containing all available category metadata.
The search is case-insensitive and scoped to the current site for security and data isolation.
"""

LOCATION_SEARCH_DESCRIPTION = """
Search for Locations within the current site using flexible, combinable search criteria.

This powerful search endpoint allows you to find locations using multiple optional search parameters.
At least one search parameter must be provided to perform the search. You can combine multiple
criteria to narrow down results effectively.

**Search Parameters (all optional, but at least one required):**

1. **floor_id** - Filter by specific floor ID (exact match)
   - Must be a valid MongoDB ObjectId
   - Returns only locations on the specified floor

1.5. **location_type** - Filter by location type (exact match)
   - Available types: TENANT, AMENITIES, PARKING, SEATING, GATE, SECURITY_CHECKPOINT,
     BUILDING, ENTRANCE, SHUTTLE, KIOSK, MAP_OBJECT, CONNECTION
   - Use to find specific types of locations (e.g., all parking areas, all gates)

2. **amenity_type** - Filter by amenity type (exact match, only for AMENITIES locations)
   - Case-sensitive exact match against the amenity field
   - Only applies to locations where type="AMENITIES"
   - Examples: "restroom", "atm", "elevator", "prayer-room"

3. **category_id** - Filter by category ID (exact match)
   - Must be a valid MongoDB ObjectId
   - Returns locations that belong to the specified category

4. **tags** - Filter by services offered and location highlights (array, matches any)
   - Provide an array of tag strings representing services or highlights
   - Returns locations that offer ANY of the specified services or highlights
   - Case-sensitive matching
   - Examples: "wifi", "wheelchair_accessible", "24_hours", "drive_through"

5. **name** - Filter by location name (case-insensitive partial match)
   - Performs case-insensitive contains/substring search
   - Useful for finding locations with partial name knowledge
   - Examples: "coffee" might find "Starbucks Coffee", "Coffee Bean", etc.

**Usage Examples:**

### Basic Amenity Searches:
- **Find restrooms:** `search_locations(amenity_type="restroom")`
- **Find ATMs:** `search_locations(amenity_type="atm")`
- **Find elevators:** `search_locations(amenity_type="elevator")`
- **Find information desks:** `search_locations(amenity_type="information-desk")`

### Floor-Specific Searches:
- **Restrooms on ground floor:** `search_locations(amenity_type="restroom", floor_id="<ground_floor_id>")`
- **All locations on floor 2:** `search_locations(floor_id="<floor_2_id>")`
- **Elevators on current floor:** `search_locations(amenity_type="elevator", floor_id="<current_floor_id>")`

### Category-Based Searches:
- **All restaurants:** `search_locations(category_id="<restaurant_category_id>")`
- **Popular restaurants:** `search_locations(category_id="<restaurant_category_id>", tags=["popular"])`
- **Coffee shops:** `search_locations(name="coffee")`

### Service-Based Searches:
- **WiFi locations:** `search_locations(tags=["wifi"])`
- **Wheelchair accessible:** `search_locations(tags=["wheelchair_accessible"])`
- **24-hour services:** `search_locations(tags=["24_hours"])`

### Complex Multi-Parameter Searches:
- **Coffee shops with WiFi on floor 2:** `search_locations(name="coffee", tags=["wifi"], floor_id="<floor_2_id>")`
- **Accessible restaurants:** `search_locations(category_id="<restaurant_category_id>", tags=["wheelchair_accessible"])`

**Response:**
Returns an array of `LocationInfo` objects containing all available location metadata including:
- Location ID, name, and description
- Associated site ID, floor ID, and category IDs
- Geographic coordinates and service/highlight tags
- Amenity type and status information

**Security & Performance:**
- All searches are automatically scoped to the current site for security
- Results are cached using Redis for improved performance
- MongoDB aggregation pipeline ensures efficient multi-field searches
- Empty result sets are handled gracefully

**Validation:**
- At least one search parameter must be provided
- ObjectId fields (floor_id, category_id) are validated for proper format
- Invalid parameters result in clear error messages
- Site-scoped security ensures data isolation

This search functionality is ideal for:
- Location discovery and exploration
- Wayfinding and navigation assistance
- Content filtering and categorization
- Building location-aware applications
- Implementing advanced search features in venue apps
"""
