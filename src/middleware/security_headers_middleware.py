"""Security headers middleware for the Beco MCP Server.

This middleware applies comprehensive security headers to HTTP responses
based on the configured security policy.
"""

import logging
import time
from typing import Any

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.types import AS<PERSON>App

log = logging.getLogger(__name__)

# Constants for CSP directives
CSP_SELF = "'self'"


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to apply security headers to HTTP responses.

    This middleware adds comprehensive security headers to all HTTP responses
    based on the provided configuration. It follows OWASP security best practices
    and provides protection against common web vulnerabilities.

    Features:
    - Content Security Policy (CSP)
    - HTTP Strict Transport Security (HSTS)
    - X-Frame-Options
    - X-Content-Type-Options
    - X-XSS-Protection
    - Referrer Policy
    - Permissions Policy
    - Cross-Origin policies (COEP, COOP, CORP)
    - Server header modification
    - Custom security headers
    """

    def __init__(self, app: ASGIApp, config: dict[str, Any] | None = None):
        """Initialize the security headers middleware.

        Args:
            app: The ASGI application
            config: Security headers configuration dict. If None, uses default configuration.
        """
        super().__init__(app)
        self.config = config or {}

        # Pre-build header values for performance
        self._prebuilt_headers = self._prebuild_headers()

        log.info("SecurityHeadersMiddleware initialized with configuration")

    def _prebuild_headers(self) -> dict[str, str]:
        """Pre-build static security headers for performance.

        Returns:
            Dict of header names to values for static headers
        """
        try:
            headers = {}

            # Build core security headers
            self._add_csp_header(headers)
            self._add_hsts_header(headers)
            self._add_basic_security_headers(headers)
            self._add_cross_origin_headers(headers)
            self._add_custom_headers(headers)

            return headers
        except Exception:
            log.exception("Failed to prebuild security headers")
            # Continue with empty headers rather than failing
            return {}

    def _add_csp_header(self, headers: dict[str, str]) -> None:
        """Add Content Security Policy header if enabled."""
        if not self.config.get("enable_csp", True):
            return

        csp_value = self._build_csp_header()
        if csp_value:
            header_name = (
                "Content-Security-Policy-Report-Only"
                if self.config.get("csp_report_only", False)
                else "Content-Security-Policy"
            )
            headers[header_name] = csp_value

    def _add_hsts_header(self, headers: dict[str, str]) -> None:
        """Add HSTS header if enabled."""
        if not self.config.get("enable_hsts", True):
            return

        hsts_value = self._build_hsts_header()
        if hsts_value:
            headers["Strict-Transport-Security"] = hsts_value

    def _add_basic_security_headers(self, headers: dict[str, str]) -> None:
        """Add basic security headers (Frame Options, Content Type, XSS, Referrer)."""
        # X-Frame-Options
        if self.config.get("enable_frame_options", True):
            headers["X-Frame-Options"] = self.config.get("frame_options", "DENY")

        # X-Content-Type-Options
        if self.config.get("enable_content_type_options", True):
            headers["X-Content-Type-Options"] = "nosniff"

        # X-XSS-Protection
        if self.config.get("enable_xss_protection", True):
            headers["X-XSS-Protection"] = self.config.get("xss_protection_value", "1; mode=block")

        # Referrer Policy
        if self.config.get("enable_referrer_policy", True):
            headers["Referrer-Policy"] = self.config.get("referrer_policy", "strict-origin-when-cross-origin")

        # Permissions Policy
        if self.config.get("enable_permissions_policy", True):
            pp_value = self._build_permissions_policy_header()
            if pp_value:
                headers["Permissions-Policy"] = pp_value

    def _add_cross_origin_headers(self, headers: dict[str, str]) -> None:
        """Add cross-origin security headers."""
        # Cross-Origin Embedder Policy
        if self.config.get("enable_coep", False):
            headers["Cross-Origin-Embedder-Policy"] = self.config.get("coep_value", "require-corp")

        # Cross-Origin Opener Policy
        if self.config.get("enable_coop", True):
            headers["Cross-Origin-Opener-Policy"] = self.config.get("coop_value", "same-origin")

        # Cross-Origin Resource Policy
        if self.config.get("enable_corp", True):
            headers["Cross-Origin-Resource-Policy"] = self.config.get("corp_value", "same-origin")

    def _add_custom_headers(self, headers: dict[str, str]) -> None:
        """Add any custom headers from configuration."""
        custom_headers = self.config.get("custom_headers", {})
        headers.update(custom_headers)

    def _build_csp_header(self) -> str:
        """Build the Content Security Policy header value."""
        # Default CSP directives
        default_directives = {
            "default-src": [CSP_SELF],
            "script-src": [CSP_SELF, "'unsafe-inline'"],
            "style-src": [CSP_SELF, "'unsafe-inline'"],
            "img-src": [CSP_SELF, "data:", "https:"],
            "connect-src": [CSP_SELF],
            "font-src": [CSP_SELF],
            "object-src": ["'none'"],
            "frame-ancestors": ["'none'"],
            "base-uri": [CSP_SELF],
            "form-action": [CSP_SELF],
        }

        csp_directives = self.config.get("csp_directives", default_directives)

        directives = []
        for directive, sources in csp_directives.items():
            if sources:
                directives.append(f"{directive} {' '.join(sources)}")

        csp_value = "; ".join(directives)

        csp_report_uri = self.config.get("csp_report_uri")
        if csp_report_uri:
            csp_value += f"; report-uri {csp_report_uri}"

        return csp_value

    def _build_hsts_header(self) -> str:
        """Build the HSTS header value."""
        hsts_max_age = self.config.get("hsts_max_age", 31536000)  # 1 year
        hsts_value = f"max-age={hsts_max_age}"

        if self.config.get("hsts_include_subdomains", True):
            hsts_value += "; includeSubDomains"

        if self.config.get("hsts_preload", False):
            hsts_value += "; preload"

        return hsts_value

    def _build_permissions_policy_header(self) -> str:
        """Build the Permissions Policy header value."""
        default_policies = {
            "camera": ["'none'"],
            "microphone": ["'none'"],
            "geolocation": ["'none'"],
            "payment": ["'none'"],
            "usb": ["'none'"],
            "magnetometer": ["'none'"],
            "gyroscope": ["'none'"],
            "accelerometer": ["'none'"],
        }

        permissions_policy = self.config.get("permissions_policy", default_policies)

        policies = []
        for feature, allowlist in permissions_policy.items():
            if allowlist:
                policies.append(f"{feature}=({' '.join(allowlist)})")
            else:
                policies.append(f"{feature}=()")

        return ", ".join(policies)

    def _is_https_request(self, request: Request) -> bool:
        """Determine if a request should be considered HTTPS.

        This method checks both the direct request scheme and proxy headers
        to properly handle requests behind SSL-terminating reverse proxies.

        Args:
            request: The HTTP request to check

        Returns:
            bool: True if the request should be considered HTTPS
        """
        # Check direct scheme first
        if request.url.scheme == "https":
            return True

        # Check if proxy header trust is enabled
        if not self.config.get("trust_proxy_headers", False):
            return False

        # Check common proxy headers for HTTPS indication
        headers = request.headers

        # X-Forwarded-Proto is the most common proxy header
        forwarded_proto = headers.get("x-forwarded-proto", "").lower()
        if forwarded_proto == "https":
            return True

        # X-Forwarded-Ssl header (used by some proxies)
        forwarded_ssl = headers.get("x-forwarded-ssl", "").lower()
        if forwarded_ssl in ("on", "true", "1"):
            return True

        # X-Scheme header (used by some proxies)
        x_scheme = headers.get("x-scheme", "").lower()
        if x_scheme == "https":
            return True

        # Front-End-Https header (used by Microsoft proxies)
        frontend_https = headers.get("front-end-https", "").lower()
        if frontend_https in ("on", "true", "1"):
            return True

        return False

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Process the request and add security headers to the response.

        Args:
            request: The incoming HTTP request
            call_next: The next middleware or endpoint in the chain

        Returns:
            Response with security headers applied
        """
        start_time = time.monotonic()

        try:
            # Process the request
            response = await call_next(request)

            # Apply security headers
            self._apply_security_headers(request, response)

            # Log security header application
            duration_ms = (time.monotonic() - start_time) * 1000
            log.debug(f"Security headers applied to {request.method} {request.url.path} in {duration_ms:.2f}ms")

            return response

        except Exception:
            log.exception("Error in SecurityHeadersMiddleware")
            # Try to get response from call_next anyway
            try:
                response = await call_next(request)
                return response
            except Exception:
                log.exception("Failed to process request after security middleware error")
                raise

    def _apply_security_headers(self, request: Request, response: Response) -> None:
        """Apply security headers to the response.

        Args:
            request: The HTTP request
            response: The HTTP response to modify
        """
        try:
            # Apply pre-built static headers
            for header_name, header_value in self._prebuilt_headers.items():
                response.headers[header_name] = header_value

            # Handle Server header modification
            if self.config.get("hide_server_header", True):
                custom_server_header = self.config.get("custom_server_header")
                if custom_server_header:
                    response.headers["Server"] = custom_server_header
                else:
                    # Remove the Server header if it exists
                    if "Server" in response.headers:
                        del response.headers["Server"]

            # Apply conditional headers based on request context
            self._apply_conditional_headers(request, response)

        except Exception:
            log.exception("Failed to apply security headers")
            # Don't fail the request if header application fails

    def _apply_conditional_headers(self, request: Request, response: Response) -> None:
        """Apply headers that depend on request context.

        Args:
            request: The HTTP request
            response: The HTTP response to modify
        """
        try:
            # Only apply HSTS to HTTPS requests (including those behind SSL-terminating proxies)
            if self.config.get("enable_hsts", True) and not self._is_https_request(request):
                # Remove HSTS header for non-HTTPS requests
                if "Strict-Transport-Security" in response.headers:
                    del response.headers["Strict-Transport-Security"]
                log.debug("Removed HSTS header for non-HTTPS request")

            # Adjust CSP for specific paths (e.g., documentation) only if CSP is enabled
            # This prevents applying relaxed CSP when CSP is globally disabled
            if self.config.get("enable_csp", True) and (
                request.url.path.startswith("/docs")
                or request.url.path.startswith("/redoc")
                or request.url.path.startswith("/openapi.json")
            ):
                # Swagger UI and ReDoc need relaxed CSP
                self._apply_relaxed_csp_for_documentation(response)

        except Exception:
            log.exception("Failed to apply conditional security headers")

    def _apply_relaxed_csp_for_documentation(self, response: Response) -> None:
        """Apply relaxed CSP for documentation endpoints (/docs, /redoc, /openapi.json).

        Swagger UI and ReDoc require more permissive CSP policies to function properly,
        including unsafe-inline scripts, unsafe-eval, and blob: sources.

        Args:
            response: The HTTP response to modify
        """
        try:
            # Create a more permissive CSP for Swagger UI and ReDoc
            relaxed_csp = (
                f"default-src {CSP_SELF}; "
                f"script-src {CSP_SELF} 'unsafe-inline' 'unsafe-eval' blob:; "
                f"style-src {CSP_SELF} 'unsafe-inline' https://fonts.googleapis.com; "
                f"img-src {CSP_SELF} data: https: blob:; "
                f"font-src {CSP_SELF} data: https://fonts.gstatic.com; "
                f"connect-src {CSP_SELF}; "
                f"worker-src {CSP_SELF} blob:; "
                f"child-src {CSP_SELF} blob:; "
                "object-src 'none'; "
                f"base-uri {CSP_SELF}"
            )

            header_name = (
                "Content-Security-Policy-Report-Only"
                if self.config.get("csp_report_only", False)
                else "Content-Security-Policy"
            )
            response.headers[header_name] = relaxed_csp

            log.debug("Applied relaxed CSP for documentation endpoint (Swagger UI/ReDoc)")

        except Exception:
            log.exception("Failed to apply relaxed CSP for documentation")

    def get_applied_headers(self) -> dict[str, str]:
        """Get the headers that would be applied by this middleware.

        Returns:
            Dictionary of header names to values
        """
        return self._prebuilt_headers.copy()

    def get_config(self) -> dict[str, Any]:
        """Get the current security configuration.

        Returns:
            The security headers configuration
        """
        return self.config.copy()
