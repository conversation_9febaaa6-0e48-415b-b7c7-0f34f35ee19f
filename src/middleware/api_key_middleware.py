import logging

from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import JSONResponse

from src.auth.api_key_manager import ApiKeyManager
from src.constants.headers import (
    HTTP_HEADER_RATE_LIMIT_MAX_ATTEMPTS,
    HTTP_HEADER_RATE_LIMIT_REMAINING_ATTEMPTS,
    HTTP_HEADER_RETRY_AFTER,
)
from src.constants.request_context import (
    REQUEST_RATE_LIMIT_MAX_ATTEMPTS,
    REQUEST_RATE_LIMIT_REMAINING_ATTEMPTS,
    REQUEST_RATE_LIMIT_RETRY_AFTER,
)
from src.context.request_context import RequestContextManager

log = logging.getLogger(__name__)

EXCLUDED_PATHS = [
    "/docs",
    "/redoc",
    "/openapi.json",
    "/health",
    "/api/v1/health",
    "/.well-known",
]


class APIKeyMiddleware(BaseHTTPMiddleware):
    """
    Middleware to enforce API key authentication.
    Validates the X-API-Key header and logs the result.
    """

    def __init__(self, app):
        super().__init__(app)
        self.api_key_manager = ApiKeyManager(app)
        self.excluded_paths = EXCLUDED_PATHS

    def add_rate_limit_headers(self, response, is_rate_limited=False):
        """
        Add rate limit headers to the response if available in context.
        Always include X-RateLimit-Limit and X-RateLimit-Remaining-Attempts.
        Include Retry-After (seconds) only when rate limit is exceeded (429) and retry_after is available.
        """
        max_attempts = RequestContextManager.get(REQUEST_RATE_LIMIT_MAX_ATTEMPTS)
        remaining_attempts = RequestContextManager.get(REQUEST_RATE_LIMIT_REMAINING_ATTEMPTS)
        retry_after = RequestContextManager.get(REQUEST_RATE_LIMIT_RETRY_AFTER)  # seconds until reset (only on 429)

        # Always include these headers if available
        if max_attempts is not None:
            response.headers[HTTP_HEADER_RATE_LIMIT_MAX_ATTEMPTS] = str(max_attempts)
        if remaining_attempts is not None:
            response.headers[HTTP_HEADER_RATE_LIMIT_REMAINING_ATTEMPTS] = str(remaining_attempts)

        # Only include Retry-After when rate limit is exceeded AND retry_after is available
        if is_rate_limited and retry_after is not None:
            response.headers[HTTP_HEADER_RETRY_AFTER] = str(retry_after)

        return response

    async def dispatch(self, request: Request, call_next):
        """
        Dispatch method to handle incoming requests.
        """
        try:
            # Skip API key check for excluded paths
            if any(request.url.path.startswith(path) for path in self.excluded_paths):
                return await call_next(request)

            try:
                await self.api_key_manager.authenticate(request)
                log.info(f"API key authentication succeeded for path: {request.url.path}")
                response = await call_next(request)
                return self.add_rate_limit_headers(response)

            except Exception as e:
                log.warning(f"API key authentication failed for path: {request.url.path}")
                # Determine if this is a rate limit error
                is_rate_limited = getattr(e, "status_code", 401) == 429
                # Create error response with rate limit headers
                response = JSONResponse(
                    status_code=getattr(e, "status_code", 401),
                    content={"detail": f"API key authentication failed: {getattr(e, 'detail', str(e))}"},
                )
                return self.add_rate_limit_headers(response, is_rate_limited=is_rate_limited)

        finally:
            RequestContextManager.clear()
