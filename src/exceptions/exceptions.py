"""Business-specific exception classes."""

from .base_exception import BaseHTTPException


class AlreadySetupError(BaseHTTPException):
    """Raised when the system has already been set up."""

    error_code = "already_setup"
    description = (
        "System has already been successfully installed. Please refresh the page or return to the dashboard homepage."
    )
    code = 403


class NotSetupError(BaseHTTPException):
    """Raised when the system has not been set up yet."""

    error_code = "not_setup"
    description = "System has not been set up yet. Please complete the setup process first."
    code = 400


class ResourceNotFoundError(BaseHTTPException):
    """Raised when a requested resource is not found."""

    error_code = "resource_not_found"
    description = "The requested resource was not found."
    code = 404


class ValidationError(BaseHTTPException):
    """Raised when request validation fails."""

    error_code = "validation_error"
    description = "Request validation failed."
    code = 400


class AuthenticationError(BaseHTTPException):
    """Raised when authentication fails."""

    error_code = "authentication_error"
    description = "Authentication failed."
    code = 401


class AuthorizationError(BaseHTTPException):
    """Raised when authorization fails."""

    error_code = "authorization_error"
    description = "You don't have permission to perform this action."
    code = 403


class InternalServerError(BaseHTTPException):
    """Raised for internal server errors."""

    error_code = "internal_server_error"
    description = "An internal server error occurred."
    code = 500


class ServiceUnavailableError(BaseHTTPException):
    """Raised when a service is temporarily unavailable."""

    error_code = "service_unavailable"
    description = "Service is temporarily unavailable."
    code = 503
