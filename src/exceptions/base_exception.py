"""Base exception classes for the application."""

import logging

from fastapi import HTTPException

log = logging.getLogger(__name__)


class BaseHTTPException(HTTPException):
    """Base HTTP exception with error code and description."""

    error_code: str = "unknown_error"
    description: str = "An unknown error occurred"
    code: int = 500

    def __init__(self, detail: str | None = None, log_error: bool = True):
        super().__init__(status_code=self.code, detail=detail or self.description)

        # Log the error if requested
        if log_error:
            self._log_error(detail)

    def _log_error(self, detail: str | None = None):
        """Log the error with appropriate level based on status code."""
        error_message = detail or self.description

        if self.code >= 500:
            # Server errors - log as ERROR
            log.error(
                f"HTTP {self.code} - {self.error_code}: {error_message}",
                extra={"error_code": self.error_code, "status_code": self.code, "detail": error_message},
            )
        elif self.code >= 400:
            # Client errors - log as WARNING
            log.warning(
                f"HTTP {self.code} - {self.error_code}: {error_message}",
                extra={"error_code": self.error_code, "status_code": self.code, "detail": error_message},
            )
        else:
            # Other errors - log as INFO
            log.info(
                f"HTTP {self.code} - {self.error_code}: {error_message}",
                extra={"error_code": self.error_code, "status_code": self.code, "detail": error_message},
            )
