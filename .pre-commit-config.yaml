repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-added-large-files
        exclude: core/model_runtime/model_providers/__base/tokenizers/gpt2/vocab.json
      - id: check-ast
      - id: check-case-conflict
      - id: check-json
      - id: check-toml
      - id: check-yaml
      - id: trailing-whitespace
      - id: end-of-file-fixer
  - repo: https://github.com/PyCQA/autoflake
    rev: v2.3.1
    hooks:
      - id: autoflake
        args: [--remove-all-unused-imports, --in-place]
        stages: [pre-commit]
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.19.1
    hooks:
      - id: pyupgrade
        args: [--py310-plus]
        stages: [pre-commit]
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.5
    hooks:
      - id: ruff
        args: [--fix]
        stages: [pre-commit]
      - id: ruff-format
        stages: [pre-commit]
  - repo: https://github.com/PyCQA/bandit
    rev: 1.8.0
    hooks:
      - id: bandit
        args: ["-c", "pyproject.toml"]
        additional_dependencies: [".[toml]"]
        stages: [pre-push]
