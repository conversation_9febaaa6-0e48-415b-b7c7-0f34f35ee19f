[project]
name = "beco-mcp-server"
dynamic = ["version"]
description = "beco mcp server"
readme = "README.md"
requires-python = ">=3.12"

dependencies = [
    "beanie>=1.30.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.10.0",
    "python-dotenv>=1.0.0",
    "pytz>=2025.2",
    "uvicorn[standard]>=0.24.0",
]
# Before adding new dependency, consider place it in
# alphabet order (a-z) and suitable group.

[tool.setuptools.dynamic]
version = {attr = "src.__version__"}

[tool.uv]
default-groups = ["mcp", "db", "api"]
package = false

[dependency-groups]

############################################################
# [ Dev ] dependency group
# Required for development and running tests
############################################################
dev = [
    "coverage~=7.2.4",
    "dotenv-linter~=0.5.0",
    "faker~=32.1.0",
    "lxml-stubs~=0.5.1",
    "mypy~=1.16.0",
    "ruff~=0.11.5",
    "pytest~=8.3.2",
    "pytest-benchmark~=4.0.0",
    "pytest-cov~=4.1.0",
    "pytest-env~=1.1.3",
    "pytest-mock~=3.14.0",
    "types-aiofiles~=24.1.0",
    "types-beautifulsoup4~=4.12.0",
    "types-cachetools~=5.5.0",
    "types-colorama~=0.4.15",
    "types-defusedxml~=0.7.0",
    "types-deprecated~=1.2.15",
    "types-docutils~=0.21.0",
    "types-jsonschema~=4.23.0",
    "types-flask-cors~=5.0.0",
    "types-flask-migrate~=4.1.0",
    "types-gevent~=24.11.0",
    "types-greenlet~=3.1.0",
    "types-html5lib~=1.1.11",
    "types-markdown~=3.7.0",
    "types-oauthlib~=3.2.0",
    "types-objgraph~=3.6.0",
    "types-olefile~=0.47.0",
    "types-openpyxl~=3.1.5",
    "types-pexpect~=4.9.0",
    "types-protobuf~=5.29.1",
    "types-psutil~=7.0.0",
    "types-psycopg2~=2.9.21",
    "types-pygments~=2.19.0",
    "types-pymysql~=1.1.0",
    "types-python-dateutil~=2.9.0",
    "types-pywin32~=310.0.0",
    "types-pyyaml~=6.0.12",
    "types-regex~=2024.11.6",
    "types-requests~=2.32.0",
    "types-requests-oauthlib~=2.0.0",
    "types-shapely~=2.0.0",
    "types-simplejson>=3.20.0",
    "types-six>=1.17.0",
    "types-tensorflow>=2.18.0",
    "types-tqdm>=4.67.0",
    "types-ujson>=5.10.0",
    "boto3-stubs>=1.38.20",
    "types-jmespath>=1.0.2.20240106",
    "types_pyOpenSSL>=24.1.0",
    "types_cffi>=1.17.0",
    "types_setuptools>=80.9.0",
    "pandas-stubs~=2.2.3",
    "scipy-stubs>=********",
    "pre-commit>=4.2.0",
    "pip-audit>=2.9.0",
    "bandit[toml]>=1.8.0",
]

############################################################
# API dependencies
############################################################

api = [
    "fastapi-mcp>=0.3.4",
    "fastapi[standard]>=0.104.0",
]

############################################################
# MCP dependencies
############################################################

mcp = [
    "fastmcp>=2.9.2",
]

############################################################
# DB dependencies
############################################################

db = [
    "motor>=3.3.0",
    "pymongo>=4.13.2",
    "redis>=5.0.0",
]

[tool.bandit]
exclude_dirs = ["tests", "test", "tests_*", "test_*"]
skips = ["B101", "B601"]
targets = ["src"]
recursive = true
