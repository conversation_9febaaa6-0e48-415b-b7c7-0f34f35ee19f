# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Generate a secure key with: openssl rand -base64 42
SECRET_KEY=************************************************

# Security Extension Settings
SECURITY_ENABLED=true
SECURITY_HEADERS_ENABLED=true

# Content Security Policy (CSP) - Prevents XSS attacks
CSP_ENABLED=true
CSP_REPORT_ONLY=false
CSP_REPORT_URI=

# HTTP Strict Transport Security (HSTS) - Forces HTTPS
# Set to false for HTTP development, true for HTTPS production
HSTS_ENABLED=true
HSTS_MAX_AGE=31536000
HSTS_INCLUDE_SUBDOMAINS=true
HSTS_PRELOAD=false

# Frame Protection - Prevents clickjacking attacks
FRAME_OPTIONS_ENABLED=true
FRAME_OPTIONS_VALUE=DENY

# Content Type Protection - Prevents MIME sniffing attacks
CONTENT_TYPE_OPTIONS_ENABLED=true

# XSS Protection - Enables browser XSS filtering
XSS_PROTECTION_ENABLED=true

# Referrer Policy - Controls referrer information sharing
REFERRER_POLICY_ENABLED=true

# Permissions Policy - Controls browser feature access
PERMISSIONS_POLICY_ENABLED=true

# Cross-Origin Policies - Controls cross-origin resource sharing
COEP_ENABLED=false
COOP_ENABLED=true
CORP_ENABLED=true

# Server Header - Hide server information
HIDE_SERVER_HEADER=true
CUSTOM_SERVER_HEADER=

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

#Development Environment Configuration
DEBUG=true
DEPLOY_ENV=DEVELOPMENT

BACKEND_APP_BIND_ADDRESS=0.0.0.0
BACKEND_APP_PORT=5001
GUNICORN_TIMEOUT=360

#Log Config
# Supported values are `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`
LOG_LEVEL=INFO
# Log file path
LOG_FOLDER=logs
# Log file max size, the unit is MB
LOG_FILE_MAX_SIZE=20
# Log file max backup count
LOG_FILE_BACKUP_COUNT=5
# Log dateformat
LOG_DATEFORMAT=%Y-%m-%d %H:%M:%S
# Log Timezone
LOG_TZ=UTC
# Log format
LOG_FORMAT = "%(asctime)s,%(msecs)03d %(levelname)-2s [%(filename)s:%(lineno)d] [%(req_id)s] %(message)s"


API_COMPRESSION_ENABLED=false
HTTP_REQUEST_MAX_CONNECT_TIMEOUT=300
HTTP_REQUEST_MAX_READ_TIMEOUT=600
HTTP_REQUEST_MAX_WRITE_TIMEOUT=600
HTTP_REQUEST_NODE_MAX_BINARY_SIZE=10485760
HTTP_REQUEST_NODE_MAX_TEXT_SIZE=1048576

# MCP Server Configuration
MCP_APP_NAME="beCo-MCP-Server"
MCP_APP_VERSION=0.0.1
MCP_APP_INSTRUCTION="This is the MCP server for beCoMap. "

# API Server Configuration
APP_NAME="beCo-API-Server"
APP_VERSION=0.0.1
APP_INSTRUCTION="This is the APP server for beCoMap."

SSRF_PROXY_HTTP_URL=
SSRF_PROXY_HTTPS_URL=
SSRF_DEFAULT_MAX_RETRIES=3
SSRF_DEFAULT_TIME_OUT=5
SSRF_DEFAULT_CONNECT_TIME_OUT=5
SSRF_DEFAULT_READ_TIME_OUT=5
SSRF_DEFAULT_WRITE_TIME_OUT=5

RESPECT_XFORWARD_HEADERS_ENABLED=false

# mongo configuration
MONGO_URI="mongodb://localhost:27017"
MONGO_DB_NAME="beco_lighting_db"

DB_USERNAME=postgres
DB_PASSWORD=aiagent1234
DB_HOST=localhost
DB_PORT=5432
DB_DATABASE=aiagent

SQLALCHEMY_ECHO=false

# redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_USERNAME=
REDIS_PASSWORD=aiagent1234
REDIS_USE_SSL=false
REDIS_DB=0

REDIS_SERIALIZATION_PROTOCOL=3

# redis Sentinel configuration.
REDIS_USE_SENTINEL=false
REDIS_SENTINELS=
REDIS_SENTINEL_SERVICE_NAME=
REDIS_SENTINEL_USERNAME=
REDIS_SENTINEL_PASSWORD=
REDIS_SENTINEL_SOCKET_TIMEOUT=0.1

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_API_KEY_MAX_ATTEMPTS=10
RATE_LIMIT_API_KEY_TIME_WINDOW=3600
