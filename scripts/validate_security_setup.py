#!/usr/bin/env python3
"""
Standalone Security Setup Validation Script

This script validates that the security extension is properly configured and working.
It can be run independently without complex dependencies.
"""

import argparse
import json
import os
import sys
import time
from typing import Any
from urllib.parse import urljoin

# Try to import requests, but provide fallback
try:
    import requests

    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False
    print("⚠️  Warning: 'requests' library not available. HTTP testing will be skipped.")


class SecuritySetupValidator:
    """Validate security setup with minimal dependencies."""

    def __init__(self, base_url: str | None = None):
        self.base_url = base_url or "http://localhost:5001"
        self.results = {
            "timestamp": time.time(),
            "base_url": self.base_url,
            "tests": {},
            "summary": {"passed": 0, "failed": 0, "skipped": 0},
        }

    def run_all_tests(self) -> dict[str, Any]:
        """Run all security validation tests."""
        print("🔐 Security Setup Validation")
        print("=" * 50)

        # Test 1: Environment Configuration
        self._test_environment_config()

        # Test 2: Security Extension Import
        self._test_security_imports()

        # Test 3: HTTP Headers (if requests available)
        if HAS_REQUESTS:
            self._test_security_headers()
            self._test_smart_csp_relaxation()
        else:
            self._skip_test("HTTP Headers Test", "requests library not available")
            self._skip_test("Smart CSP Relaxation Test", "requests library not available")

        # Test 4: Configuration Validation
        self._test_config_validation()

        return self.results

    def _test_environment_config(self) -> None:
        """Test environment configuration."""
        test_name = "Environment Configuration"
        print(f"\n🔧 Testing {test_name}...")

        try:
            # Check for .env file
            env_file_exists = os.path.exists(".env")

            # Check for security-related environment variables
            security_vars = ["SECRET_KEY", "SECURITY_ENABLED", "CSP_ENABLED", "HSTS_ENABLED", "FRAME_OPTIONS_ENABLED"]

            found_vars = []
            missing_vars = []

            for var in security_vars:
                if os.getenv(var) is not None:
                    found_vars.append(var)
                else:
                    missing_vars.append(var)

            # Evaluate results
            if env_file_exists and len(found_vars) >= 3:
                self._pass_test(
                    test_name,
                    {"env_file_exists": env_file_exists, "found_vars": found_vars, "missing_vars": missing_vars},
                )
                print(f"  ✅ Found .env file with {len(found_vars)} security variables")
            else:
                self._fail_test(
                    test_name,
                    {
                        "env_file_exists": env_file_exists,
                        "found_vars": found_vars,
                        "missing_vars": missing_vars,
                        "error": "Missing .env file or insufficient security variables",
                    },
                )
                print("  ❌ Missing .env file or security variables")

        except Exception as e:
            self._fail_test(test_name, {"error": str(e)})
            print(f"  ❌ Error: {e}")

    def _test_security_imports(self) -> None:
        """Test that security modules can be imported."""
        test_name = "Security Module Imports"
        print(f"\n📦 Testing {test_name}...")

        try:
            # Add current directory to path
            if "." not in sys.path:
                sys.path.insert(0, ".")

            # Try to import security modules
            imports_successful = []
            import_errors = []

            modules_to_test = [
                ("src.extensions.ext_security", "Security Extension"),
                ("src.middleware.security_headers_middleware", "Security Middleware"),
                ("src.utils.security_utils", "Security Utils"),
            ]

            for module_name, display_name in modules_to_test:
                try:
                    __import__(module_name)
                    imports_successful.append(display_name)
                    print(f"  ✅ {display_name} imported successfully")
                except ImportError as e:
                    import_errors.append(f"{display_name}: {str(e)}")
                    print(f"  ❌ {display_name} import failed: {e}")

            if len(imports_successful) == len(modules_to_test):
                self._pass_test(test_name, {"successful_imports": imports_successful})
            else:
                self._fail_test(test_name, {"successful_imports": imports_successful, "import_errors": import_errors})

        except Exception as e:
            self._fail_test(test_name, {"error": str(e)})
            print(f"  ❌ Unexpected error: {e}")

    def _test_security_headers(self) -> None:
        """Test that security headers are being applied."""
        test_name = "Security Headers Application"
        print(f"\n🛡️  Testing {test_name}...")

        if not HAS_REQUESTS:
            self._skip_test(test_name, "requests library not available")
            return

        try:
            # Test main endpoint
            response = requests.get(self.base_url, timeout=10)
            headers = dict(response.headers)

            # Check for required security headers
            required_headers = ["X-Content-Type-Options", "X-Frame-Options"]

            optional_headers = [
                "Content-Security-Policy",
                "Content-Security-Policy-Report-Only",
                "Strict-Transport-Security",
                "X-XSS-Protection",
                "Referrer-Policy",
            ]

            found_required = []
            missing_required = []
            found_optional = []

            for header in required_headers:
                if header in headers:
                    found_required.append(header)
                    print(f"  ✅ {header}: {headers[header]}")
                else:
                    missing_required.append(header)
                    print(f"  ❌ Missing: {header}")

            for header in optional_headers:
                if header in headers:
                    found_optional.append(header)
                    print(f"  ✅ {header}: {headers[header][:50]}...")

            if len(missing_required) == 0:
                self._pass_test(
                    test_name,
                    {
                        "status_code": response.status_code,
                        "found_required": found_required,
                        "found_optional": found_optional,
                        "total_headers": len(found_required) + len(found_optional),
                    },
                )
            else:
                self._fail_test(
                    test_name,
                    {
                        "status_code": response.status_code,
                        "found_required": found_required,
                        "missing_required": missing_required,
                        "found_optional": found_optional,
                    },
                )

        except requests.RequestException as e:
            self._fail_test(test_name, {"error": f"HTTP request failed: {str(e)}", "url": self.base_url})
            print(f"  ❌ Cannot connect to {self.base_url}: {e}")
        except Exception as e:
            self._fail_test(test_name, {"error": str(e)})
            print(f"  ❌ Unexpected error: {e}")

    def _test_smart_csp_relaxation(self) -> None:
        """Test smart CSP relaxation for documentation endpoints."""
        test_name = "Smart CSP Relaxation"
        print(f"\n🧠 Testing {test_name}...")

        if not HAS_REQUESTS:
            self._skip_test(test_name, "requests library not available")
            return

        try:
            endpoints_to_test = ["/docs", "/redoc", "/openapi.json"]
            results = {}

            for endpoint in endpoints_to_test:
                url = urljoin(self.base_url, endpoint)
                try:
                    response = requests.get(url, timeout=10)
                    headers = dict(response.headers)

                    # Check for CSP header (either enforced or report-only)
                    csp_header = headers.get("Content-Security-Policy") or headers.get(
                        "Content-Security-Policy-Report-Only"
                    )

                    if csp_header:
                        # Check if CSP is relaxed (contains unsafe-inline or unsafe-eval)
                        is_relaxed = "unsafe-inline" in csp_header or "unsafe-eval" in csp_header
                        results[endpoint] = {
                            "accessible": response.status_code < 400,
                            "has_csp": True,
                            "is_relaxed": is_relaxed,
                            "status_code": response.status_code,
                        }

                        status = "✅" if is_relaxed else "⚠️"
                        relaxed_text = "relaxed" if is_relaxed else "strict"
                        print(f"  {status} {endpoint}: {relaxed_text} CSP (status: {response.status_code})")
                    else:
                        results[endpoint] = {
                            "accessible": response.status_code < 400,
                            "has_csp": False,
                            "is_relaxed": False,
                            "status_code": response.status_code,
                        }
                        print(f"  ⚠️  {endpoint}: No CSP header found (status: {response.status_code})")

                except requests.RequestException as e:
                    results[endpoint] = {"accessible": False, "error": str(e)}
                    print(f"  ❌ {endpoint}: Connection failed - {e}")

            # Evaluate overall results
            accessible_docs = sum(1 for r in results.values() if r.get("accessible", False))
            relaxed_csp_count = sum(1 for r in results.values() if r.get("is_relaxed", False))

            if accessible_docs >= 2:  # At least /docs and /redoc should work
                self._pass_test(
                    test_name,
                    {
                        "endpoint_results": results,
                        "accessible_endpoints": accessible_docs,
                        "relaxed_csp_count": relaxed_csp_count,
                    },
                )
            else:
                self._fail_test(
                    test_name,
                    {
                        "endpoint_results": results,
                        "accessible_endpoints": accessible_docs,
                        "error": "Documentation endpoints not accessible",
                    },
                )

        except Exception as e:
            self._fail_test(test_name, {"error": str(e)})
            print(f"  ❌ Unexpected error: {e}")

    def _test_config_validation(self) -> None:
        """Test configuration validation functionality."""
        test_name = "Configuration Validation"
        print(f"\n⚙️  Testing {test_name}...")

        try:
            # Try to import and use validation functions
            from src.utils.security_utils import get_security_info, validate_security_config

            # Test with a sample configuration
            test_config = {
                "enable_csp": True,
                "enable_hsts": True,
                "enable_frame_options": True,
                "enable_content_type_options": True,
            }

            # Run validation
            validation_result = validate_security_config(test_config)
            security_info = get_security_info(test_config)

            # Check results
            if isinstance(validation_result, dict) and "is_valid" in validation_result:
                score = security_info.get("security_score", 0)
                self._pass_test(
                    test_name,
                    {"validation_works": True, "security_score": score, "is_valid": validation_result["is_valid"]},
                )
                print(f"  ✅ Validation functions work (Security Score: {score}/100)")
            else:
                self._fail_test(test_name, {"validation_works": False, "error": "Invalid validation result format"})
                print("  ❌ Validation functions returned unexpected format")

        except ImportError as e:
            self._fail_test(test_name, {"validation_works": False, "error": f"Import error: {str(e)}"})
            print(f"  ❌ Cannot import validation functions: {e}")
        except Exception as e:
            self._fail_test(test_name, {"validation_works": False, "error": str(e)})
            print(f"  ❌ Validation test failed: {e}")

    def _pass_test(self, test_name: str, details: dict[str, Any]) -> None:
        """Record a passed test."""
        self.results["tests"][test_name] = {"status": "PASSED", "details": details}
        self.results["summary"]["passed"] += 1

    def _fail_test(self, test_name: str, details: dict[str, Any]) -> None:
        """Record a failed test."""
        self.results["tests"][test_name] = {"status": "FAILED", "details": details}
        self.results["summary"]["failed"] += 1

    def _skip_test(self, test_name: str, reason: str) -> None:
        """Record a skipped test."""
        self.results["tests"][test_name] = {"status": "SKIPPED", "reason": reason}
        self.results["summary"]["skipped"] += 1
        print(f"\n⏭️  Skipping {test_name}: {reason}")

    def print_summary(self) -> None:
        """Print test summary and recommendations."""
        summary = self.results["summary"]
        total_tests = summary["passed"] + summary["failed"] + summary["skipped"]

        print("\n📊 Test Summary")
        print("=" * 30)
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"⏭️  Skipped: {summary['skipped']}")

        # Overall status
        if summary["failed"] == 0:
            print("\n🎉 Overall Status: PASSED")
            print("Your security setup is working correctly!")
        else:
            print("\n⚠️  Overall Status: NEEDS ATTENTION")
            print("Some security tests failed. Please review the issues above.")

        # Recommendations
        print("\n💡 Recommendations:")

        failed_tests = [name for name, result in self.results["tests"].items() if result["status"] == "FAILED"]

        if "Environment Configuration" in failed_tests:
            print("  • Create a .env file with security configuration")
            print("  • Copy from .env.example and customize for your environment")

        if "Security Module Imports" in failed_tests:
            print("  • Check that all security files are present in src/ directory")
            print("  • Verify Python path and module structure")

        if "Security Headers Application" in failed_tests:
            print("  • Start your application server")
            print("  • Check that security extension is enabled in configuration")

        if "Smart CSP Relaxation" in failed_tests:
            print("  • Verify documentation endpoints are accessible")
            print("  • Check CSP configuration and smart relaxation logic")

        if summary["failed"] == 0:
            print("  • Your security setup is working well!")
            print("  • Consider running: python scripts/generate_security_report.py")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Validate security setup")
    parser.add_argument(
        "--url", default="http://localhost:5001", help="Base URL to test (default: http://localhost:5001)"
    )
    parser.add_argument("--json", action="store_true", help="Output results in JSON format")

    args = parser.parse_args()

    # Run validation
    validator = SecuritySetupValidator(args.url)
    results = validator.run_all_tests()

    if args.json:
        print(json.dumps(results, indent=2, default=str))
    else:
        validator.print_summary()

    # Exit with appropriate code
    if results["summary"]["failed"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
